import express from 'express';
import {
  funnelLogin,
  createPaymentIntent,
  postDischargeLetter,
  postDischargeLetterstatus,
  postQuestionnaire,
  registerPatient,
  validateSession,
  validateUser,
  encryptURL,
  postHealthCheckSurvey,
  checkOtp,
  resendOtp,
  getPatientByleadID,
  createPrescriptionLead,
  updateZohoLeadCancelBooking,
  updateZohoLeadBookingStatus,
  updateZohoLeadBookingOnlineStatus,
  updateZohoLeadMemberStatus,
  getZohoLeadById,
  postHarvestDischarge,
  postHarvestNoAlcohol,
  submitConsentForm,
  getConsentFormStatus,
  funnelLoginEmailPassword,
  getPatientByEmail,
  getLastConsultationByZohoId,
  updateZohoLeadReachConfirmationPage,
  getZohoInvoiceByOrderId,
  getZohoPaymentLinkByOrderId,
  setZohoInvoicePaid,
  postPaymentSteps,
  forgotPassword,
  resendOtpReset,
  resetOtpPasswordProcess,
  setZohoInvoicePaymentStatus,
  getStockUpdates,
  storeUTMParamsOnLead,
  submitTHCIncreaseQuestionnaire,
  getTHCQuestionnaireStatus,
  submitExtendTPQuestionnaire,
  getExtendTPQuestionnaireStatus,
  submitAdd22THCQuestionnaire,
  getAdd22THCQuestionnaireStatus,
  submitQuantityIncreaseQuestionnaire,
  getQuantityIncreaseQuestionnaireStatus,
  getPatientQuantityStatus,
  updateApplicationStatus
} from '../controllers/funnel';

import multer from 'multer';
import { postDecryptedUrl } from '../controllers/calendar';


const upload = multer({});

const router = express.Router();
const currentVersion = 'v1.0';

router.get(`/${currentVersion}/patient/validate`, validateSession);
router.get(`/${currentVersion}/user/validate`, validateUser);
router.get(`/${currentVersion}/url/encrypt`, encryptURL);

router.post(`/${currentVersion}/register`, registerPatient);
router.post(`/${currentVersion}/questionnaire`, validateUser, postQuestionnaire);
router.post(`/${currentVersion}/discharge-letter`, validateUser, upload.any(), postDischargeLetter);
router.post(`/${currentVersion}/discharge-letter-status`, validateUser, postDischargeLetterstatus);
router.post(`/${currentVersion}/prescription-lead`, createPrescriptionLead);
router.get(`/${currentVersion}/patient/bylead/:leadId`, getPatientByleadID);
router.post(`/${currentVersion}/patient/check-otp`, validateUser, checkOtp);
router.post(`/${currentVersion}/patient/resend-otp`, validateUser, resendOtp);

router.post(`/${currentVersion}/patient/questionnaire/upload`, validateUser, postQuestionnaire);
router.post(`/${currentVersion}/patient/discharge-letter/upload`, validateUser, upload.any(), postDischargeLetter);
router.post(`/${currentVersion}/patient/harvest/discharge`, postHarvestDischarge);
router.post(`/${currentVersion}/patient/harvest/no-alcohol`, postHarvestNoAlcohol);

// Consent form endpoints
router.post(`/${currentVersion}/patient/consent-form`, submitConsentForm); // Removed validateUser middleware
router.get(`/${currentVersion}/patient/consent-form/status`, getConsentFormStatus); // Removed validateUser middleware

// Patient details endpoints
router.get(`/${currentVersion}/patient/details`, getPatientByEmail);
router.get(`/${currentVersion}/patient/consent-form/status-by-email`, getPatientByEmail);

router.post(`/${currentVersion}/patient/discharge-letter/status`, validateUser, postDischargeLetterstatus);
router.post(`/${currentVersion}/patient/create-hold`, validateUser, createPaymentIntent);
router.post(`/${currentVersion}/patient/encrypt-id`, validateUser, encryptURL);
router.post(`/${currentVersion}/patient/decrypt-id`, postDecryptedUrl);
router.post(`/${currentVersion}/patient/updateZohoLeadCancelBooking`, updateZohoLeadCancelBooking);
router.post(`/${currentVersion}/patient/updateZohoLeadBookingOnlineStatus`, updateZohoLeadBookingOnlineStatus);
router.post(`/${currentVersion}/patient/updateZohoLeadBookingStatus`, updateZohoLeadBookingStatus);
router.post(`/${currentVersion}/patient/updateZohoLeadMemberStatus`, updateZohoLeadMemberStatus);
router.post(`/${currentVersion}/patient/updateZohoLeadReachConfirmationPage`, updateZohoLeadReachConfirmationPage);
router.get(`/${currentVersion}/patient/getZohoLeadById/:leadId`, getZohoLeadById);
router.get(`/${currentVersion}/patient/getZohoInvoiceByOrderId/:orderId`, getZohoInvoiceByOrderId);
router.get(`/${currentVersion}/patient/getZohoPaymentLinkByOrderId/:orderId`, getZohoPaymentLinkByOrderId);
router.get(`/${currentVersion}/patient/setZohoInvoicePaid/:paymentNumber`, setZohoInvoicePaid);
router.post(`/${currentVersion}/patient/setZohoInvoicePaymentStatus`, setZohoInvoicePaymentStatus);
router.get(`/${currentVersion}/patient/consultation/:zohoId`, getLastConsultationByZohoId);
router.post(`/${currentVersion}/login`, funnelLogin);
router.post(`/${currentVersion}/patient/register`, registerPatient);
router.post(`/${currentVersion}/patient/login`, funnelLogin);
router.post(`/${currentVersion}/patient/login/email-password`, funnelLoginEmailPassword);
router.post(`/${currentVersion}/patient/health-survey`, postHealthCheckSurvey);
router.post(`/${currentVersion}/patient/syncPaymentSteps`, postPaymentSteps);
router.post(`/${currentVersion}/patient/forgot-password`, forgotPassword);
router.post(`/${currentVersion}/patient/resend-otp-password`, resendOtpReset);
router.post(`/${currentVersion}/patient/otp-reset-password`, resetOtpPasswordProcess);
router.get(`/${currentVersion}/patient/wordpress/getStock`, getStockUpdates);
router.post(`/${currentVersion}/patient/storeUtmParams/:LeadID`, storeUTMParamsOnLead);
router.post(`/${currentVersion}/patient/update-application-status`, updateApplicationStatus);

// THC increase questionnaire endpoints
router.post(`/${currentVersion}/patient/thc-increase-questionnaire`, validateUser, submitTHCIncreaseQuestionnaire);
router.get(`/${currentVersion}/patient/thc-increase-questionnaire/status`, validateUser, getTHCQuestionnaireStatus);

// ExtendTP questionnaire endpoints
router.post(`/${currentVersion}/patient/extend-tp-questionnaire`, validateUser, submitExtendTPQuestionnaire);
router.get(`/${currentVersion}/patient/extend-tp-questionnaire/status`, validateUser, getExtendTPQuestionnaireStatus);

// Add 22% THC questionnaire endpoints
router.post(`/${currentVersion}/patient/add-22-thc-questionnaire`, validateUser, submitAdd22THCQuestionnaire);
router.get(`/${currentVersion}/patient/add-22-thc-questionnaire/status`, validateUser, getAdd22THCQuestionnaireStatus);

// Quantity increase questionnaire endpoints
router.post(`/${currentVersion}/patient/quantity-increase-questionnaire`, validateUser, submitQuantityIncreaseQuestionnaire);
router.get(`/${currentVersion}/patient/quantity-increase-questionnaire/status`, validateUser, getQuantityIncreaseQuestionnaireStatus);
router.get(`/${currentVersion}/patient/quantity-status`, validateUser, getPatientQuantityStatus);

export default router;
