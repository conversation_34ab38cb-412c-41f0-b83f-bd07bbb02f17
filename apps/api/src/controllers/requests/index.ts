import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';
import { ApiResponse } from '../../helpers/response';
import { ApiError } from '../../utils/ApiError';
import { db } from '../../utils/db';
import axios from 'axios';
import { ZohoAuth, zohoLeadURL } from '../../helpers/zoho';
import { logger } from '../../config/logger';
import { getFormatedZohoDate } from '../../utils/misc';
import { getCookie } from '../../utils/cookie';
import { ZohoTreatmentPlan } from '../../types';
import { requestModerationService } from '../../services/requestModeration.service';
import { updateContactRejectionNotes, updateContactTreatmentPlan } from '../zoho';

// Interface for Zoho contact data structure (reusing from zoho controller)
interface ZohoContact {
  id: string;
  Email?: string;
  First_Name?: string;
  Last_Name?: string;
  Full_Name?: string;
  Phone?: string;
  Mobile?: string;
  Created_Time: string;
  Modified_Time?: string;
  Member_Status?: string;
  Member_ID1?: string;
  Doctor_Notes?: string;
  Mental_Health_Supporting_Documentation?: string;
  Consulting_Doctor?: string;
  Supply_Date_1?: string;
  Supply_Expiration?: string;
  Strength_Concentration?: string;
  Total_Qty_22_1?: string;
  Total_Qty_29_1?: string;
  Dose_Per_Day_22_1?: string;
  Dose_Per_Day_29_1?: string;
  Number_of_Repeats_1?: string;
  Next_Repeat_1?: string;
  Dispensing_Interval_Period_1?: string;
  Maximum_Doses_per_Day_22_1?: string;
  Maximum_Doses_per_Day_29_1?: string;
  Pouch_Count_22_1?: string;
  Pouch_Count_29_1?: string;
}



// Define interface for questionnaire request
interface QuestionnaireRequest {
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  id: string;
  patient_id: string;
  email: string;
  questionnaire_data: unknown;
  total_score: number;
  max_score: number;
  is_eligible: boolean;
  status: string;
  created_at: string;
  patient_name: string;
  patient_dob: string;
  patient_email: string;
  db_doctor_name: string | null;
  db_doctor_id: string | null;
  treatment_plan_source: 'database' | 'needs_zoho_check';
  slack_message_ts: string | null;
  strength_requests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }> | null;
}

// Helper function to create prescription lead in Zoho
const createPrescriptionLead = async (patient: { fullName: string; email: string; mobile: string }): Promise<string> => {
  const [firstName, ...lastNameParts] = patient.fullName.split(' ');
  const lastName = lastNameParts.join(' ');

  const headers = await ZohoAuth.getHeaders();
  const leadData = {
    data: [{
      Email: patient.email,
      Phone: patient.mobile,
      Mobile: patient.mobile,
      First_Name: firstName,
      Last_Name: lastName,
      Existing_Patient: 'yes',
      Is_Stored_In_DB: 'yes',
      Lead_Status: 'Pre-Qualified'
    }]
  };

  const leadResult = await axios.post(zohoLeadURL, leadData, { headers });
  return leadResult.data.data?.[0].details?.id;
};

// Helper function to get patient by ID
const getPatientById = async (client: { query: (query: string, params?: string[]) => Promise<{ rows: { patientID: string; email: string; fullName: string; mobile: string }[] }> }, patientId: string): Promise<{ patientID: string; email: string; fullName: string; mobile: string }> => {
  logger.info(`Looking for patient with patientID: "${patientId}"`);

  const patientQuery = `SELECT * FROM patient WHERE "patientID" = $1`;
  const patientResult = await client.query(patientQuery, [patientId]);

  logger.info(`Patient query result: found ${patientResult.rows.length} patients`);

  if (patientResult.rows.length === 0) {
    // Let's also try a broader search to see what patients exist
    const allPatientsQuery = `SELECT "patientID", email, "fullName" FROM patient LIMIT 5`;
    const allPatientsResult = await client.query(allPatientsQuery);
    logger.info(`Sample patients in database:`, allPatientsResult.rows);

    throw new Error('Patient not found');
  }

  return patientResult.rows[0];
};

// Helper function to get Zoho contact ID by email
const getZohoContactIdByEmail = async (email: string): Promise<string | null> => {
  try {
    logger.info(`Getting Zoho contact ID for email: ${email}`);
    const response = await ZohoAuth.getZohoContactByEmail(email);

    if (response && response.data && response.data.length > 0) {
      // Sort contacts by Created_Time descending (most recent first)
      const sortedContacts = response.data.sort((a: ZohoContact, b: ZohoContact) => {
        const aTime = new Date(a.Created_Time).getTime();
        const bTime = new Date(b.Created_Time).getTime();
        return bTime - aTime;
      });
      const contact = sortedContacts[0];
      logger.info(`Found Zoho contact ID ${contact.id} for email: ${email}`);
      return contact.id;
    } else {
      logger.warn(`No Zoho contact found for email: ${email}`);
      return null;
    }
  } catch (error) {
    logger.error(`Error fetching Zoho contact ID for email ${email}:`, error);
    return null;
  }
};

// Helper function to get Doctor_Decision_In_Messenger value based on request type and approval status
const getDoctorDecisionValue = (requestType: string, isApproved: boolean): string => {
  const decisionPrefix = isApproved ? 'approved' : 'rejected';

  switch (requestType) {
    case 'thc_increase':
      return `${decisionPrefix}_adding_29`;
    case 'extend_tp':
      return `${decisionPrefix}_tp_extension`;
    case 'add_22_thc':
      return `${decisionPrefix}_adding_22`;
    case 'quantity_increase':
      return `${decisionPrefix}_increase_quantity`;
    default:
      return `${decisionPrefix}_${requestType}`;
  }
};

// Helper function to get Zoho treatment plan by email
const getZohoTreatmentPlanByEmail = async (email: string): Promise<ZohoTreatmentPlan | null> => {
  try {
    // First get the contact by email
    const response = await ZohoAuth.getZohoContactByEmail(email);

    if (!response || !response.data || response.data.length === 0) {
      return null;
    }

    // Sort contacts by Created_Time descending (most recent first)
    const sortedContacts = response.data.sort((a: ZohoContact, b: ZohoContact) => {
      const aTime = new Date(a.Created_Time).getTime();
      const bTime = new Date(b.Created_Time).getTime();
      return bTime - aTime;
    });

    const contactData = sortedContacts[0];

    // Map Zoho contact data to ZohoTreatmentPlan structure
    const treatmentPlan: ZohoTreatmentPlan = {
      consultingDoctor: contactData.Consulting_Doctor || contactData.Full_Name || '',
      treatmentPlanStartDate: contactData.Supply_Date_1 || '',
      treatmentPlanEndDate: contactData.Supply_Expiration || '',
      thcContent: contactData.Strength_Concentration || `22%: ${contactData.Total_Qty_22_1 || '0'}g, 29%: ${contactData.Total_Qty_29_1 || '0'}g`,
      totalAllowance: {
        thc22: contactData.Total_Qty_22_1 || '0',
        thc29: contactData.Total_Qty_29_1 || '0'
      },
      totalAllowanceUsed: {
        thc22: 'N/A - initial order not placed',
        thc29: 'N/A - initial order not placed'
      },
      repeatAllowance: {
        thc22: contactData.Dose_Per_Day_22_1 || '0',
        thc29: contactData.Dose_Per_Day_29_1 || '0'
      },
      numberOfRepeats: parseInt(contactData.Number_of_Repeats_1 || '0'),
      repeatsRemaining: {
        thc22: parseInt(contactData.Number_of_Repeats_1) || 0,
        thc29: parseInt(contactData.Number_of_Repeats_1) || 0
      },
      nextRepeatDate: {
        thc22: contactData.Next_Repeat_1 || 'N/A',
        thc29: contactData.Next_Repeat_1 || 'N/A'
      },
      supplyRemainingForRepeat: {
        thc22: 'N/A',
        thc29: 'N/A'
      }
    };

    return treatmentPlan;
  } catch (error) {
    logger.error(`Error fetching Zoho treatment plan for email ${email}:`, error);
    return null;
  }
};

// Helper function to batch process Zoho treatment plan requests
export const batchGetZohoTreatmentPlans = async (patientEmails: string[]): Promise<Map<string, ZohoTreatmentPlan>> => {
  if (patientEmails.length === 0) return new Map();

  const treatmentPlans = new Map<string, ZohoTreatmentPlan>();

  // Process in smaller batches to avoid rate limiting
  const batchSize = 5; // Conservative batch size for Zoho API
  for (let i = 0; i < patientEmails.length; i += batchSize) {
    const batch = patientEmails.slice(i, i + batchSize);
    const promises = batch.map(email => getZohoTreatmentPlanByEmail(email));
    const results = await Promise.allSettled(promises);

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        treatmentPlans.set(batch[index], result.value);
      }
    });

    // Add delay between batches to respect rate limits
    if (i + batchSize < patientEmails.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return treatmentPlans;
};



export const getPendingRequests: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  try {
    // Check if we should include all statuses (for "All" filter)
    const includeAll = req.query.includeAll === 'true';

    // Step 1: Get current doctor's information from cookie
    const cookie = req.headers.cookie;
    const doctorAccessID = getCookie(cookie);

    if (!doctorAccessID) {
      res.status(401).json(
        new ApiResponse(httpStatus.UNAUTHORIZED, 'Doctor authentication required', null, false)
      );
      return;
    }

    // Get current doctor's name and status for filtering
    const doctorQuery = `SELECT name, username, status FROM Dr WHERE "accessID" = $1`;
    const doctorResult = await client.query(doctorQuery, [doctorAccessID]);

    if (doctorResult.rows.length === 0) {
      res.status(404).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Doctor not found', null, false)
      );
      return;
    }

    const currentDoctor = doctorResult.rows[0];
    const currentDoctorName = currentDoctor.name || currentDoctor.username;

    // Check if current doctor is Anjum
    const isAnjumDoctor = currentDoctorName.toLowerCase().includes('anjum');

   

    // Step 2: Query requests with latest database treatment plan info
    const statusFilter = includeAll
      ? `('pending', 'submitted', 'approved', 'rejected')`
      : `('pending', 'submitted')`;

    const query = `
      WITH LatestTreatmentPlans AS (
        SELECT DISTINCT ON ("patientID")
          "patientID",
          "drName",
          "drId",
          "createdAt",
          email
        FROM TreatmentPlan
        ORDER BY "patientID", "createdAt" DESC
      )
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.patient_id,
        tiq.email,
        tiq.questionnaire_data,
        tiq.total_score,
        tiq.max_score,
        tiq.is_eligible,
        tiq.status,
        tiq.created_at,
        tiq.approved_at,
        tiq.approved_by,
        tiq.review_notes,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        p.email as patient_email,
        ltp."drName" as db_doctor_name,
        ltp."drId" as db_doctor_id,
        tiq.slack_message_ts,
        NULL::jsonb as strength_requests,
        CASE
          WHEN ltp."drName" IS NOT NULL OR ltp."drId" IS NOT NULL THEN 'database'
          ELSE 'needs_zoho_check'
        END as treatment_plan_source
      FROM thc_increase_questionnaire tiq
      LEFT JOIN patient p ON tiq.patient_id = p."patientID"
      LEFT JOIN LatestTreatmentPlans ltp ON tiq.patient_id = ltp."patientID"
      WHERE tiq.status IN ${statusFilter} AND tiq.is_eligible = true

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.patient_id,
        etq.email,
        etq.questionnaire_data,
        etq.total_score,
        etq.max_score,
        etq.is_eligible,
        etq.status,
        etq.created_at,
        etq.approved_at,
        etq.approved_by,
        etq.review_notes,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        p.email as patient_email,
        ltp."drName" as db_doctor_name,
        ltp."drId" as db_doctor_id,
        etq.slack_message_ts,
        NULL::jsonb as strength_requests,
        CASE
          WHEN ltp."drName" IS NOT NULL OR ltp."drId" IS NOT NULL THEN 'database'
          ELSE 'needs_zoho_check'
        END as treatment_plan_source
      FROM extend_tp_questionnaire etq
      LEFT JOIN patient p ON etq.patient_id = p."patientID"
      LEFT JOIN LatestTreatmentPlans ltp ON etq.patient_id = ltp."patientID"
      WHERE etq.status IN ${statusFilter} AND etq.is_eligible = true

      UNION ALL

      SELECT
        'add_22_thc' as type,
        atq.id,
        atq.patient_id,
        atq.email,
        atq.questionnaire_data,
        atq.total_score,
        atq.max_score,
        atq.is_eligible,
        atq.status,
        atq.created_at,
        atq.approved_at,
        atq.approved_by,
        atq.review_notes,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        p.email as patient_email,
        ltp."drName" as db_doctor_name,
        ltp."drId" as db_doctor_id,
        atq.slack_message_ts,
        NULL::jsonb as strength_requests,
        CASE
          WHEN ltp."drName" IS NOT NULL OR ltp."drId" IS NOT NULL THEN 'database'
          ELSE 'needs_zoho_check'
        END as treatment_plan_source
      FROM add_22_thc_questionnaire atq
      LEFT JOIN patient p ON atq.patient_id = p."patientID"
      LEFT JOIN LatestTreatmentPlans ltp ON atq.patient_id = ltp."patientID"
      WHERE atq.status IN ${statusFilter} AND atq.is_eligible = true

      UNION ALL

      SELECT
        'quantity_increase' as type,
        qiq.id,
        qiq.patient_id,
        qiq.email,
        qiq.questionnaire_data,
        qiq.total_score,
        qiq.max_score,
        qiq.is_eligible,
        qiq.status,
        qiq.created_at,
        qiq.approved_at,
        qiq.approved_by,
        qiq.review_notes,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        p.email as patient_email,
        ltp."drName" as db_doctor_name,
        ltp."drId" as db_doctor_id,
        qiq.slack_message_ts,
        qiq.strength_requests,
        CASE
          WHEN ltp."drName" IS NOT NULL OR ltp."drId" IS NOT NULL THEN 'database'
          ELSE 'needs_zoho_check'
        END as treatment_plan_source
      FROM quantity_increase_questionnaire qiq
      LEFT JOIN patient p ON qiq.patient_id = p."patientID"
      LEFT JOIN LatestTreatmentPlans ltp ON qiq.patient_id = ltp."patientID"
      WHERE qiq.status IN ${statusFilter} AND qiq.is_eligible = true

      ORDER BY created_at DESC
    `;

    const result = await client.query(query);
    const allRequests = result.rows;

    // Step 3: Filter with hierarchy: Database → Zoho → Exclude
    const filteredRequests: QuestionnaireRequest[] = [];
    const needZohoCheck: QuestionnaireRequest[] = [];

    for (const request of allRequests) {
      // Priority 1: Check database treatment plan
      if (request.db_doctor_name === currentDoctorName || request.db_doctor_id === doctorAccessID) {
        filteredRequests.push(request);
        //logger.info(`Including request ${request.id} - matched database doctor: ${request.db_doctor_name || request.db_doctor_id}`);
        continue;
      }

      // Special case: If current doctor is Anjum, also include requests from inactive doctors
      if (isAnjumDoctor && request.db_doctor_name) {
        // Check if the request's doctor is inactive
        const requestDoctorStatusQuery = `SELECT status FROM Dr WHERE name = $1 OR username = $1`;
        const requestDoctorStatusResult = await client.query(requestDoctorStatusQuery, [request.db_doctor_name]);

        if (requestDoctorStatusResult.rows.length > 0) {
          const requestDoctorStatus = requestDoctorStatusResult.rows[0].status;
          if (requestDoctorStatus !== 'active') {
            filteredRequests.push(request);
            //logger.info(`Including request ${request.id} for Anjum - original doctor ${request.db_doctor_name} is inactive (status: ${requestDoctorStatus})`);
            continue;
          }
        }
      }

      // Priority 2: If no database treatment plan, check Zoho
      if (!request.db_doctor_name && !request.db_doctor_id) {
        needZohoCheck.push(request);
        continue;
      }

      // Priority 3: Database shows different doctor, exclude
      //logger.info(`Excluding request ${request.id} - database shows different doctor: ${request.db_doctor_name || request.db_doctor_id}`);
    }

    // Step 4: Batch check Zoho only for patients without database treatment plans
    if (needZohoCheck.length > 0) {
      logger.info(`Checking Zoho for ${needZohoCheck.length} patients without database treatment plans`);

      const zohoResults = await batchGetZohoTreatmentPlans(
        needZohoCheck.map(r => r.patient_email)
      );

      for (const request of needZohoCheck) {
        const zohoTreatmentPlan = zohoResults.get(request.patient_email);

        // Check if matches current doctor
        if (zohoTreatmentPlan?.consultingDoctor === currentDoctorName) {
          filteredRequests.push(request);
          //logger.info(`Including request ${request.id} - matched Zoho consulting doctor: ${zohoTreatmentPlan?.consultingDoctor}`);
          continue;
        }

        // Special case: If current doctor is Anjum, also check if Zoho doctor is inactive
        if (isAnjumDoctor && zohoTreatmentPlan?.consultingDoctor) {
          const zohoDoctorStatusQuery = `SELECT status FROM Dr WHERE name = $1 OR username = $1`;
          const zohoDoctorStatusResult = await client.query(zohoDoctorStatusQuery, [zohoTreatmentPlan.consultingDoctor]);

          if (zohoDoctorStatusResult.rows.length > 0) {
            const zohoDoctorStatus = zohoDoctorStatusResult.rows[0].status;
            if (zohoDoctorStatus !== 'active') {
              filteredRequests.push(request);
              //logger.info(`Including request ${request.id} for Anjum - Zoho doctor ${zohoTreatmentPlan.consultingDoctor} is inactive (status: ${zohoDoctorStatus})`);
              continue;
            }
          }
        }

        // Handle orphaned requests: If no treatment plan exists in either database or Zoho, assign to Anjum
        if (!zohoTreatmentPlan?.consultingDoctor) {
          if (isAnjumDoctor) {
            filteredRequests.push(request);
            logger.info(`Including orphaned request ${request.id} for Anjum - no treatment plan found in database or Zoho`);
            continue;
          } else {
            logger.info(`Orphaned request ${request.id} - no treatment plan found, should be assigned to Anjum`);
          }
        } else {
          //logger.info(`Excluding request ${request.id} - Zoho consulting doctor: ${zohoTreatmentPlan.consultingDoctor}`);
        }
      }
    }

   
    // Calculate counts for filtered requests
    const counts = includeAll ? {
      all: filteredRequests.length,
      requests: filteredRequests.filter((r: QuestionnaireRequest) => r.status === 'pending' || r.status === 'submitted').length,
      unread: filteredRequests.filter((r: QuestionnaireRequest) => r.status === 'pending').length
    } : {
      all: filteredRequests.length,
      requests: filteredRequests.length,
      unread: filteredRequests.filter((r: QuestionnaireRequest) => r.status === 'pending').length
    };

   
    //logger.info(`Doctor ${currentDoctorName} sees ${filteredRequests.length} requests (${allRequests.length} total, ${needZohoCheck.length} checked via Zoho)`);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', { requests: filteredRequests, counts }, true)
    );
  } catch (error) {
    console.error('Error fetching pending requests:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
      new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to fetch pending requests', null, false)
    );
  } finally {
    client.release();
  }
});

export const approveRequest: RequestHandler = catchAll(async (req, res) => {
  const { id } = req.params;
  const { doctorId, doctorName } = req.body;

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Get questionnaire data
    const questionnaireQuery = `
      SELECT
        'thc_increase' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM thc_increase_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'extend_tp' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM extend_tp_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'add_22_thc' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM add_22_thc_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'quantity_increase' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        selected_strengths, strength_requests
      FROM quantity_increase_questionnaire WHERE id = $1
    `;
    const questionnaireResult = await client.query(questionnaireQuery, [id]);

    if (questionnaireResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Request not found', null, false)
      );
      return;
    }

    const questionnaire = questionnaireResult.rows[0];
    const patientId = questionnaire.patient_id; // Get patient ID from questionnaire

    logger.info(`Approving request ${id} for patient ${patientId}`);
    logger.info(`Questionnaire data:`, questionnaire);

    if (questionnaire.type === 'thc_increase') {
      try {
        // Get patient details and create prescription lead
        const patient = await getPatientById(client, patientId);
        const prescriptionLeadId = await createPrescriptionLead(patient);

      // Create 29% treatment plan
      const treatmentPlanQuery = `
        INSERT INTO TreatmentPlan (
          "patientID", "drId", outcome, "drNotes", "diagnosis", date, "drName",
          "strengthAndConcentration29", "dosePerDay29", "maxDose29", "totalQuantity29",
          "numberOfRepeat29", "supplyInterval29", email, type, source
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      `;

      const drNotes = `Approved THC increase to 29% based on questionnaire. Risk score: ${questionnaire.total_score}/${questionnaire.max_score}`;

      await client.query(treatmentPlanQuery, [
        patientId, doctorId, 'Approve Unrestricted', drNotes, 'THC Increase Request', new Date().toISOString(), doctorName,
        '29%', '0.1', '1.0', '28', '6', '28', patient.email, 'treatmentplan', 'messenger'
      ]);

      // Send treatment plan to Zoho (using existing format from chat controller)
      const zohoTreatmentPlanData = {
        data: [{
          Dr_Approve_Date_Time: getFormatedZohoDate(),
          Strength_Concentration: '29%',
          Dr_Trigger: 'Approve Unrestricted',
          Consulting_Doctor: doctorName,
          Prescription_Date_1: getFormatedZohoDate().split('T')[0],
          Dose_Per_Day_29: '0.1',
          Maximum_Doses_per_Day_29: '1.0',
          Total_Qty_29_1: '28',
          Number_of_Repeats_29: '6',
          Approved_Using_Messenger: 'Yes',
          Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
          Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
        }]
      };

      const headers = await ZohoAuth.getHeaders();
      await axios.put(`${zohoLeadURL}/${prescriptionLeadId}`, zohoTreatmentPlanData, { headers });

      // Update the Zoho contact directly with the number of repeats to ensure it's not ignored during lead merge
      const zohoContactId = await getZohoContactIdByEmail(questionnaire.email);
      if (zohoContactId) {
        try {
          // Calculate supply dates: current date and 6 months from now
          const currentDate = new Date();
          const supplyExpiryDate = new Date(currentDate);
          supplyExpiryDate.setMonth(supplyExpiryDate.getMonth() + 6);

          const contactUpdateData: Record<string, string | number> = {
            Number_of_Repeats_1: '6',
            Supply_Date_1: getFormatedZohoDate().split('T')[0],
            Supply_Expiration: getFormatedZohoDate(supplyExpiryDate.toISOString()).split('T')[0],
            Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
            Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
          };

          const contactUpdateSuccess = await updateContactTreatmentPlan(zohoContactId, contactUpdateData);
          if (contactUpdateSuccess) {
            logger.info(`Successfully updated Zoho contact ${zohoContactId} with number of repeats and supply dates for THC increase`);
          } else {
            logger.warn(`Failed to update Zoho contact ${zohoContactId} with number of repeats and supply dates, but THC increase was successful`);
          }
        } catch (contactError) {
          // Log the error but don't fail the approval process
          logger.error(`Error updating Zoho contact ${zohoContactId} with number of repeats and supply dates:`, contactError);
        }
      } else {
        logger.warn(`No Zoho contact found for email ${questionnaire.email}, skipping direct contact update`);
      }

      logger.info(`Created 29% THC treatment plan for patient ${patientId} with prescription lead ${prescriptionLeadId}`);
      } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Error in THC increase approval:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
          new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, `Error in THC increase approval: ${errorMessage}`, null, false)
        );
        return;
      }
    }

    if (questionnaire.type === 'extend_tp') {
      try {
        // Get patient details
        const patient = await getPatientById(client, patientId);

      // Get latest treatment plan to extend
      const latestPlanQuery = `
        SELECT * FROM TreatmentPlan
        WHERE "patientID" = $1
        ORDER BY "createdAt" DESC
        LIMIT 1
      `;
      const latestPlanResult = await client.query(latestPlanQuery, [patientId]);

      if (latestPlanResult.rows.length === 0) {
        await client.query('ROLLBACK');
        res.status(httpStatus.NOT_FOUND).json(
          new ApiResponse(httpStatus.NOT_FOUND, 'No existing treatment plan found to extend', null, false)
        );
        return;
      }

      const latestPlan = latestPlanResult.rows[0];

        // Create prescription lead in Zoho
        const prescriptionLeadId = await createPrescriptionLead(patient);

      // Create extended treatment plan with same strengths but numberOfRepeat = 6
      const treatmentPlanQuery = `
        INSERT INTO TreatmentPlan (
          "patientID", "drId", outcome, "drNotes", "diagnosis", date, "drName",
          "strengthAndConcentration22", "dosePerDay22", "maxDose22", "totalQuantity22", "numberOfRepeat22", "supplyInterval22",
          "strengthAndConcentration29", "dosePerDay29", "maxDose29", "totalQuantity29", "numberOfRepeat29", "supplyInterval29",
          email, type, source
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22)
      `;

      const drNotes = `Approved treatment plan extension based on questionnaire. Risk score: ${questionnaire.total_score}/${questionnaire.max_score}`;

      await client.query(treatmentPlanQuery, [
        patientId, doctorId, 'Approve Unrestricted', drNotes, 'Treatment Plan Extension', new Date().toISOString(), doctorName,
        latestPlan.strengthAndConcentration22, latestPlan.dosePerDay22, latestPlan.maxDose22, latestPlan.totalQuantity22, '6', latestPlan.supplyInterval22,
        latestPlan.strengthAndConcentration29, latestPlan.dosePerDay29, latestPlan.maxDose29, latestPlan.totalQuantity29, '6', latestPlan.supplyInterval29,
        patient.email, 'treatmentplan', 'messenger'
      ]);

      // Send treatment plan to Zoho (using existing format from chat controller)
      const zohoTreatmentPlanData = {
        data: [{
          Dr_Approve_Date_Time: getFormatedZohoDate(),
          Strength_Concentration: latestPlan.strengthAndConcentration22 || latestPlan.strengthAndConcentration29,
          Dr_Trigger: 'Approve Unrestricted',
          Consulting_Doctor: doctorName,
          Prescription_Date_1: getFormatedZohoDate().split('T')[0],
          Approved_Using_Messenger: 'Yes',
          Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
          Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true),
          // 22% fields (if they exist)
          ...(latestPlan.dosePerDay22 && {
            Specified_Dose: String(latestPlan.dosePerDay22),
            Maximum_Doses_per_Day: String(latestPlan.maxDose22),
            Total_Qty_22_1: String(latestPlan.totalQuantity22),
            Number_of_Repeats_22: '6'
          }),
          // 29% fields (if they exist)
          ...(latestPlan.dosePerDay29 && {
            Dose_Per_Day_29: String(latestPlan.dosePerDay29),
            Maximum_Doses_per_Day_29: String(latestPlan.maxDose29),
            Total_Qty_29_1: String(latestPlan.totalQuantity29),
            Number_of_Repeats_29: '6'
          })
        }]
      };

      const headers = await ZohoAuth.getHeaders();
      await axios.put(`${zohoLeadURL}/${prescriptionLeadId}`, zohoTreatmentPlanData, { headers });

      // Update the Zoho contact directly with the number of repeats to ensure it's not ignored during lead merge
      const zohoContactId = await getZohoContactIdByEmail(questionnaire.email);
      if (zohoContactId) {
        try {
          // Calculate supply dates: current date and 6 months from now
          const currentDate = new Date();
          const supplyExpiryDate = new Date(currentDate);
          supplyExpiryDate.setMonth(supplyExpiryDate.getMonth() + 6);

          const contactUpdateData: Record<string, string | number> = {
            Number_of_Repeats_1: '6',
            Supply_Date_1: getFormatedZohoDate().split('T')[0],
            Supply_Expiration: getFormatedZohoDate(supplyExpiryDate.toISOString()).split('T')[0],
            Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
            Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
          };

          // Note: Only Number_of_Repeats_1 exists in Zoho contacts, not strength-specific fields

          const contactUpdateSuccess = await updateContactTreatmentPlan(zohoContactId, contactUpdateData);
          if (contactUpdateSuccess) {
            logger.info(`Successfully updated Zoho contact ${zohoContactId} with number of repeats and supply dates for treatment plan extension`);
          } else {
            logger.warn(`Failed to update Zoho contact ${zohoContactId} with number of repeats and supply dates, but treatment plan extension was successful`);
          }
        } catch (contactError) {
          // Log the error but don't fail the approval process
          logger.error(`Error updating Zoho contact ${zohoContactId} with number of repeats and supply dates:`, contactError);
        }
      } else {
        logger.warn(`No Zoho contact found for email ${questionnaire.email}, skipping direct contact update`);
      }

      logger.info(`Extended treatment plan for patient ${patientId} with prescription lead ${prescriptionLeadId}`);
      } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Error in ExtendTP approval:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
          new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, `Error in ExtendTP approval: ${errorMessage}`, null, false)
        );
        return;
      }
    }

    if (questionnaire.type === 'add_22_thc') {
      try {
        // Get patient details
        const patient = await getPatientById(client, patientId);

        // Get latest treatment plan to add 22% THC to
        const latestPlanQuery = `
          SELECT * FROM TreatmentPlan
          WHERE "patientID" = $1
          ORDER BY "createdAt" DESC
          LIMIT 1
        `;
        const latestPlanResult = await client.query(latestPlanQuery, [patientId]);

        if (latestPlanResult.rows.length === 0) {
          await client.query('ROLLBACK');
          res.status(httpStatus.NOT_FOUND).json(
            new ApiResponse(httpStatus.NOT_FOUND, 'No existing treatment plan found to add 22% THC to', null, false)
          );
          return;
        }

        const latestPlan = latestPlanResult.rows[0];

        // Verify patient has 29% THC and no existing 22% THC
        if (!latestPlan.totalQuantity29 || latestPlan.totalQuantity29 <= 0) {
          await client.query('ROLLBACK');
          res.status(httpStatus.BAD_REQUEST).json(
            new ApiResponse(httpStatus.BAD_REQUEST, 'Patient must have active 29% THC to add 22% THC', null, false)
          );
          return;
        }

        if (latestPlan.totalQuantity22 && latestPlan.totalQuantity22 > 0) {
          await client.query('ROLLBACK');
          res.status(httpStatus.BAD_REQUEST).json(
            new ApiResponse(httpStatus.BAD_REQUEST, 'Patient already has 22% THC in their treatment plan', null, false)
          );
          return;
        }

        // Create prescription lead in Zoho
        const prescriptionLeadId = await createPrescriptionLead(patient);

        // Create treatment plan with both 22% and 29% THC (following doctor controller pattern)
        const treatmentPlanQuery = `
          INSERT INTO TreatmentPlan (
            "patientID", "drId", "consultationId", "updatedAt", outcome, "drNotes", "diagnosis", date, "drName",
            "strengthAndConcentration22", "dosePerDay22", "maxDose22", "totalQuantity22", "numberOfRepeat22", "supplyInterval22",
            "strengthAndConcentration29", "dosePerDay29", "maxDose29", "totalQuantity29", "numberOfRepeat29", "supplyInterval29",
            email, "mentalHealthSupprtingDocument", "idVerified", type, source, "createdAt"
          ) VALUES (
            $1, $2, $3, CURRENT_TIMESTAMP, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, 'treatmentplan', 'messenger', CURRENT_TIMESTAMP
          )
        `;

        const drNotes = `Approved 22% THC addition based on questionnaire. Risk score: ${questionnaire.total_score}/${questionnaire.max_score}`;

        await client.query(treatmentPlanQuery, [
          patientId, // $1 - patientID
          doctorId, // $2 - drId
          null, // $3 - consultationId (null for request-based approvals)
          'Approve Unrestricted', // $4 - outcome
          drNotes, // $5 - drNotes
          '22% THC Addition Request', // $6 - diagnosis
          new Date().toISOString(), // $7 - date
          doctorName, // $8 - drName
          '22%', // $9 - strengthAndConcentration22
          '1.0', // $10 - dosePerDay22 (our default)
          '2.0', // $11 - maxDose22 (our default)
          '28', // $12 - totalQuantity22 (our default)
          latestPlan.numberOfRepeat29 || '6', // $13 - numberOfRepeat22 (match 29%)
          '28', // $14 - supplyInterval22 (our default)
          latestPlan.strengthAndConcentration29, // $15 - strengthAndConcentration29 (preserve)
          latestPlan.dosePerDay29, // $16 - dosePerDay29 (preserve)
          latestPlan.maxDose29, // $17 - maxDose29 (preserve)
          latestPlan.totalQuantity29, // $18 - totalQuantity29 (preserve)
          latestPlan.numberOfRepeat29 || '6', // $19 - numberOfRepeat29 (preserve)
          latestPlan.supplyInterval29, // $20 - supplyInterval29 (preserve)
          patient.email, // $21 - email
          'No', // $22 - mentalHealthSupprtingDocument (default for requests)
          'Yes' // $23 - idVerified (default for existing patients)
        ]);

        // Get doctor initials from the database (following doctor controller pattern)
        let doctorInitials = '';
        if (doctorId) {
          const doctorQuery = `SELECT initials FROM Dr WHERE "accessID" = $1`;
          const doctorResult = await client.query(doctorQuery, [doctorId]);
          if (doctorResult.rows.length > 0) {
            doctorInitials = doctorResult.rows[0].initials || '';
          }
        }

        // Send treatment plan to Zoho (using exact format from doctor controller)
        const zohoTreatmentPlanData = {
          data: [{
            Dr_Approve_Date_Time: getFormatedZohoDate(),
            Strength_Concentration: '22% & 29%', // Both strengths
            Dr_Trigger: 'Approve Unrestricted',
            Mental_Health_Supporting_Documentation: 'No', // Default for request-based approvals
            ID_Verified: 'Yes', // Default for existing patients
            Consulting_Doctor: doctorName,
            Consulting_Doctor_Initials_1: doctorInitials,
            Prescription_Date_1: getFormatedZohoDate().split('T')[0],
            // 22% fields (new addition with our defaults)
            Specified_Dose: '0.1',
            Maximum_Doses_per_Day: '1.0',
            Total_Qty_22_1: '28',
            Number_of_Repeats_22: String(latestPlan.numberOfRepeat29 || '6'),
            // 29% fields (preserve existing exactly)
            Dose_Per_Day_29: String(latestPlan.dosePerDay29 || ''),
            Maximum_Doses_per_Day_29: String(latestPlan.maxDose29 || ''),
            Total_Qty_29_1: String(latestPlan.totalQuantity29 || ''),
            Number_of_Repeats_29: String(latestPlan.numberOfRepeat29 || '6'),
            // Supply interval (use 22% default, fallback to 29% existing)
            Dispensing_Interval_Period_1: String(latestPlan.supplyInterval29 || '28'),
            // Doctor notes and diagnosis
            Doctor_Notes: drNotes,
            Patient_Diagnosis: '22% THC Addition Request',
            // Approval metadata
            Approved_Using_Messenger: 'Yes',
            Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
            Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
          }]
        };

        const headers = await ZohoAuth.getHeaders();


        const zohoResponse = await axios.put(`${zohoLeadURL}/${prescriptionLeadId}`, zohoTreatmentPlanData, { headers });


        // Check for Zoho errors (following doctor controller pattern)
        if (zohoResponse?.data?.data?.[0]) {
          if (zohoResponse.data.data[0].status === 'error') {
            await client.query('ROLLBACK');
            logger.error(`Zoho error for 22% THC addition:`, zohoResponse.data.data[0]);
            throw new ApiError(httpStatus.BAD_REQUEST, `Zoho error: ${JSON.stringify(zohoResponse.data.data[0])}`);
          }

          if (zohoResponse.data.data[0].status === 'success') {
            logger.info(`Successfully updated Patient :: ${prescriptionLeadId} in Zoho for 22% THC addition`);
          }
        }

        // Update the Zoho contact directly with the number of repeats to ensure it's not ignored during lead merge
        const zohoContactId = await getZohoContactIdByEmail(questionnaire.email);
        if (zohoContactId) {
          try {
            // Calculate supply dates: current date and 6 months from now
            const currentDate = new Date();
            const supplyExpiryDate = new Date(currentDate);
            supplyExpiryDate.setMonth(supplyExpiryDate.getMonth() + 6);

            const contactUpdateData: Record<string, string | number> = {
              Number_of_Repeats_1: String(latestPlan.numberOfRepeat29 || '6'),
              Supply_Date_1: getFormatedZohoDate().split('T')[0],
              Supply_Expiration: getFormatedZohoDate(supplyExpiryDate.toISOString()).split('T')[0],
              Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
              Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
            };

            const contactUpdateSuccess = await updateContactTreatmentPlan(zohoContactId, contactUpdateData);
            if (contactUpdateSuccess) {
              logger.info(`Successfully updated Zoho contact ${zohoContactId} with number of repeats and supply dates for 22% THC addition`);
            } else {
              logger.warn(`Failed to update Zoho contact ${zohoContactId} with number of repeats and supply dates, but 22% THC addition was successful`);
            }
          } catch (contactError) {
            // Log the error but don't fail the approval process
            logger.error(`Error updating Zoho contact ${zohoContactId} with number of repeats and supply dates:`, contactError);
          }
        } else {
          logger.warn(`No Zoho contact found for email ${questionnaire.email}, skipping direct contact update`);
        }

        logger.info(`Added 22% THC to treatment plan for patient ${patientId} with prescription lead ${prescriptionLeadId}`);
      } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Error in Add 22% THC approval:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
          new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, `Error in Add 22% THC approval: ${errorMessage}`, null, false)
        );
        return;
      }
    }

    if (questionnaire.type === 'quantity_increase') {
      try {
        // Get patient details
        const patient = await getPatientById(client, patientId);

        // Get latest treatment plan to update quantities
        const latestPlanQuery = `
          SELECT * FROM TreatmentPlan
          WHERE "patientID" = $1
          ORDER BY "createdAt" DESC
          LIMIT 1
        `;
        const latestPlanResult = await client.query(latestPlanQuery, [patientId]);

        if (latestPlanResult.rows.length === 0) {
          await client.query('ROLLBACK');
          res.status(httpStatus.NOT_FOUND).json(
            new ApiResponse(httpStatus.NOT_FOUND, 'No existing treatment plan found to update quantities', null, false)
          );
          return;
        }

        const latestPlan = latestPlanResult.rows[0];
        const strengthRequests = questionnaire.strength_requests || [];

        logger.info(`Processing quantity increase for patient ${patientId}:`, {
          strengthRequests,
          currentPlan: {
            quantity22: latestPlan.totalQuantity22,
            quantity29: latestPlan.totalQuantity29
          }
        });

        // Validate that all requested strengths exist in current plan
        for (const strengthRequest of strengthRequests) {
          const currentQuantity = strengthRequest.strength === '22' ? latestPlan.totalQuantity22 : latestPlan.totalQuantity29;
          if (!currentQuantity || currentQuantity <= 0) {
            await client.query('ROLLBACK');
            res.status(httpStatus.BAD_REQUEST).json(
              new ApiResponse(httpStatus.BAD_REQUEST, `Patient does not have active ${strengthRequest.strength}% THC to increase quantity`, null, false)
            );
            return;
          }
        }

        // Create prescription lead in Zoho
        const prescriptionLeadId = await createPrescriptionLead(patient);

        // Build updated treatment plan with new quantities
        const treatmentPlanQuery = `
          INSERT INTO TreatmentPlan (
            "patientID", "drId", "consultationId", "updatedAt", outcome, "drNotes", "diagnosis", date, "drName",
            "strengthAndConcentration22", "dosePerDay22", "maxDose22", "totalQuantity22", "numberOfRepeat22", "supplyInterval22",
            "strengthAndConcentration29", "dosePerDay29", "maxDose29", "totalQuantity29", "numberOfRepeat29", "supplyInterval29",
            email, "mentalHealthSupprtingDocument", "idVerified", type, source, "createdAt"
          ) VALUES (
            $1, $2, $3, CURRENT_TIMESTAMP, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, 'treatmentplan', 'messenger', CURRENT_TIMESTAMP
          )
        `;

        const drNotes = `Approved quantity increase based on questionnaire. Risk score: ${questionnaire.total_score}/${questionnaire.max_score}`;

        // Calculate new quantities based on requests
        let new22Quantity = latestPlan.totalQuantity22;
        let new29Quantity = latestPlan.totalQuantity29;

        for (const strengthRequest of strengthRequests) {
          if (strengthRequest.strength === '22') {
            new22Quantity = strengthRequest.requestedQuantity;
          } else if (strengthRequest.strength === '29') {
            new29Quantity = strengthRequest.requestedQuantity;
          }
        }

        await client.query(treatmentPlanQuery, [
          patientId, // $1 - patientID
          doctorId, // $2 - drId
          null, // $3 - consultationId (null for request-based approvals)
          'Approve Unrestricted', // $4 - outcome
          drNotes, // $5 - drNotes
          'Quantity Increase Request', // $6 - diagnosis
          new Date().toISOString(), // $7 - date
          doctorName, // $8 - drName
          latestPlan.strengthAndConcentration22, // $9 - strengthAndConcentration22 (preserve)
          latestPlan.dosePerDay22, // $10 - dosePerDay22 (preserve)
          latestPlan.maxDose22, // $11 - maxDose22 (preserve)
          new22Quantity, // $12 - totalQuantity22 (updated)
          latestPlan.numberOfRepeat22, // $13 - numberOfRepeat22 (preserve)
          latestPlan.supplyInterval22, // $14 - supplyInterval22 (preserve)
          latestPlan.strengthAndConcentration29, // $15 - strengthAndConcentration29 (preserve)
          latestPlan.dosePerDay29, // $16 - dosePerDay29 (preserve)
          latestPlan.maxDose29, // $17 - maxDose29 (preserve)
          new29Quantity, // $18 - totalQuantity29 (updated)
          latestPlan.numberOfRepeat29, // $19 - numberOfRepeat29 (preserve)
          latestPlan.supplyInterval29, // $20 - supplyInterval29 (preserve)
          patient.email, // $21 - email
          'No', // $22 - mentalHealthSupprtingDocument (default for requests)
          'Yes' // $23 - idVerified (default for existing patients)
        ]);

        // Send treatment plan to Zoho
        const zohoTreatmentPlanData = {
          data: [{
            Dr_Approve_Date_Time: getFormatedZohoDate(),
            Strength_Concentration: [new22Quantity > 0 ? '22%' : '', new29Quantity > 0 ? '29%' : ''].filter(Boolean).join(' & '),
            Dr_Trigger: 'Approve Unrestricted',
            Mental_Health_Supporting_Documentation: 'No',
            ID_Verified: 'Yes',
            Consulting_Doctor: doctorName,
            Prescription_Date_1: getFormatedZohoDate().split('T')[0],
            // 22% fields (if exists)
            ...(new22Quantity > 0 && {
              Specified_Dose: String(latestPlan.dosePerDay22 || ''),
              Maximum_Doses_per_Day: String(latestPlan.maxDose22 || ''),
              Total_Qty_22_1: String(new22Quantity),
              Number_of_Repeats_22: String(latestPlan.numberOfRepeat22 || '6'),
            }),
            // 29% fields (if exists)
            ...(new29Quantity > 0 && {
              Dose_Per_Day_29: String(latestPlan.dosePerDay29 || ''),
              Maximum_Doses_per_Day_29: String(latestPlan.maxDose29 || ''),
              Total_Qty_29_1: String(new29Quantity),
              Number_of_Repeats_29: String(latestPlan.numberOfRepeat29 || '6'),
            }),
            // Supply interval
            Dispensing_Interval_Period_1: String(latestPlan.supplyInterval29 || latestPlan.supplyInterval22 || '28'),
            // Doctor notes
            Doctor_Notes: drNotes,
            Patient_Diagnosis: 'Quantity Increase Request',
            // Approval metadata
            Approved_Using_Messenger: 'Yes',
            Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
            Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
          }]
        };

        const headers = await ZohoAuth.getHeaders();
        const zohoResponse = await axios.put(`${zohoLeadURL}/${prescriptionLeadId}`, zohoTreatmentPlanData, { headers });

        // Check for Zoho errors
        if (zohoResponse?.data?.data?.[0]) {
          if (zohoResponse.data.data[0].status === 'error') {
            await client.query('ROLLBACK');
            logger.error(`Zoho error for quantity increase:`, zohoResponse.data.data[0]);
            throw new ApiError(httpStatus.BAD_REQUEST, `Zoho error: ${JSON.stringify(zohoResponse.data.data[0])}`);
          }
        }

        // Update the Zoho contact directly with the number of repeats and quantities to ensure they're not ignored during lead merge
        const zohoContactId = await getZohoContactIdByEmail(questionnaire.email);
        if (zohoContactId) {
          try {
            // Calculate supply dates: current date and 6 months from now
            const currentDate = new Date();
            const supplyExpiryDate = new Date(currentDate);
            supplyExpiryDate.setMonth(supplyExpiryDate.getMonth() + 6);

            const contactUpdateData: Record<string, string | number> = {
              Number_of_Repeats_1: String(latestPlan.numberOfRepeat29 || latestPlan.numberOfRepeat22 || '6'),
              Supply_Date_1: getFormatedZohoDate().split('T')[0],
              Supply_Expiration: getFormatedZohoDate(supplyExpiryDate.toISOString()).split('T')[0],
              Messenger_Approval_Notes: `Approve: ${questionnaire.type.replace('_', ' ')} request approved via messenger`,
              Doctor_Decision_In_Messenger: getDoctorDecisionValue(questionnaire.type, true)
            };

            // Update the actual quantities based on the new quantities from the request
            if (new22Quantity > 0) {
              contactUpdateData.Total_Qty_22_1 = String(new22Quantity);
            }
            if (new29Quantity > 0) {
              contactUpdateData.Total_Qty_29_1 = String(new29Quantity);
            }

            const contactUpdateSuccess = await updateContactTreatmentPlan(zohoContactId, contactUpdateData);
            if (contactUpdateSuccess) {
              logger.info(`Successfully updated Zoho contact ${zohoContactId} with number of repeats, supply dates, and quantities for quantity increase`);
            } else {
              logger.warn(`Failed to update Zoho contact ${zohoContactId} with number of repeats, supply dates, and quantities, but quantity increase was successful`);
            }
          } catch (contactError) {
            // Log the error but don't fail the approval process
            logger.error(`Error updating Zoho contact ${zohoContactId} with number of repeats, supply dates, and quantities:`, contactError);
          }
        } else {
          logger.warn(`No Zoho contact found for email ${questionnaire.email}, skipping direct contact update`);
        }

        logger.info(`Updated quantities for patient ${patientId} with prescription lead ${prescriptionLeadId}`);
      } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Error in Quantity Increase approval:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
          new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, `Error in Quantity Increase approval: ${errorMessage}`, null, false)
        );
        return;
      }
    }

    // Update questionnaire status to approved
    const table = questionnaire.type === 'thc_increase' ? 'thc_increase_questionnaire' :
                  questionnaire.type === 'extend_tp' ? 'extend_tp_questionnaire' :
                  questionnaire.type === 'add_22_thc' ? 'add_22_thc_questionnaire' :
                  'quantity_increase_questionnaire';
    await client.query(`UPDATE ${table} SET status = 'approved', approved_by = $1, approved_at = NOW() WHERE id = $2`, [doctorId, id]);

    await client.query('COMMIT');

    // Send Slack notification about the approval
    await requestModerationService.sendRequestModerationResultNotification(
      questionnaire,
      'approved',
      doctorName
    );

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'Request approved successfully', { id, status: 'approved' }, true)
    );
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error approving request:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
      new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to approve request', null, false)
    );
  } finally {
    client.release();
  }
});

export const rejectRequest: RequestHandler = catchAll(async (req, res) => {
  const { id } = req.params;
  const { rejectionNotes, doctorId } = req.body;

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Get questionnaire data
    const questionnaireQuery = `
      SELECT
        'thc_increase' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM thc_increase_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'extend_tp' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM extend_tp_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'add_22_thc' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        NULL::text[] as selected_strengths, NULL::jsonb as strength_requests
      FROM add_22_thc_questionnaire WHERE id = $1
      UNION ALL
      SELECT
        'quantity_increase' as type,
        id, patient_id, email, zoho_id, questionnaire_data, total_score, max_score, is_eligible, status,
        reviewed_by, reviewed_at, review_notes, approved_by, approved_at, approval_notes,
        ip_address, user_agent, created_at, updated_at, slack_message_ts,
        selected_strengths, strength_requests
      FROM quantity_increase_questionnaire WHERE id = $1
    `;
    const questionnaireResult = await client.query(questionnaireQuery, [id]);

    if (questionnaireResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(httpStatus.NOT_FOUND).json(
        new ApiResponse(httpStatus.NOT_FOUND, 'Request not found', null, false)
      );
      return;
    }

    const questionnaire = questionnaireResult.rows[0];

    // Get doctor name for notification
    const doctorQuery = `SELECT name, username FROM Dr WHERE "accessID" = $1`;
    const doctorResult = await client.query(doctorQuery, [doctorId]);
    const doctorName = doctorResult.rows[0]?.name || doctorResult.rows[0]?.username || 'Unknown Doctor';

    // Update questionnaire status to rejected
    const table = questionnaire.type === 'thc_increase' ? 'thc_increase_questionnaire' :
                  questionnaire.type === 'extend_tp' ? 'extend_tp_questionnaire' :
                  questionnaire.type === 'add_22_thc' ? 'add_22_thc_questionnaire' :
                  'quantity_increase_questionnaire';
    await client.query(`UPDATE ${table} SET status = 'rejected', reviewed_by = $1, reviewed_at = NOW(), review_notes = $2 WHERE id = $3`,
      [doctorId, rejectionNotes || `Rejected ${questionnaire.type.replace('_', ' ')} request`, id]);

    await client.query('COMMIT');

    // Send rejection notes to Zoho contact (non-blocking)
    if (rejectionNotes && questionnaire.email) {
      try {
        const zohoUpdateSuccess = await updateContactRejectionNotes(questionnaire.email, rejectionNotes, questionnaire.type);
        if (zohoUpdateSuccess) {
          logger.info(`Successfully updated Zoho contact with rejection notes for: ${questionnaire.email}`);
        } else {
          logger.warn(`Failed to update Zoho contact with rejection notes for: ${questionnaire.email}`);
        }
      } catch (zohoError) {
        // Log the error but don't fail the rejection process
        logger.error(`Error updating Zoho contact with rejection notes for ${questionnaire.email}:`, zohoError);
      }
    }

    // Send Slack notification about the rejection
    await requestModerationService.sendRequestModerationResultNotification(
      questionnaire,
      'rejected',
      doctorName
    );

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'Request rejected successfully', { id, status: 'rejected' }, true)
    );
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error rejecting request:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json(
      new ApiResponse(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to reject request', null, false)
    );
  } finally {
    client.release();
  }
});
