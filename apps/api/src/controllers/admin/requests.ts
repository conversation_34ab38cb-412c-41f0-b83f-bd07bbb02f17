import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';
import { ApiResponse } from '../../helpers/response';
import { db } from '../../utils/db';

/**
 * Get non-eligible requests for admin view
 */
export const getAdminNonEligibleRequests: RequestHandler = catchAll(async (req, res) => {
  const { limit = '20', page = '1' } = req.query;
  const client = await db.connect();

  try {
    const limitNum = parseInt(limit as string);
    const pageNum = parseInt(page as string);
    const offset = (pageNum - 1) * limitNum;

    // First get the total count
    const countQuery = `
      SELECT COUNT(*) as total FROM (
        SELECT id FROM thc_increase_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = false
        UNION ALL
        SELECT id FROM extend_tp_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = false
        UNION ALL
        SELECT id FROM add_22_thc_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = false
        UNION ALL
        SELECT id FROM quantity_increase_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = false
      ) combined_requests
    `;

    const countResult = await client.query(countQuery);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limitNum);

    const query = `
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.patient_id,
        tiq.email,
        tiq.questionnaire_data,
        tiq.total_score,
        tiq.max_score,
        tiq.is_eligible,
        tiq.status,
        tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM thc_increase_questionnaire tiq
      LEFT JOIN patient p ON tiq.patient_id = p."patientID"
      WHERE tiq.status IN ('pending', 'submitted') AND tiq.is_eligible = false

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.patient_id,
        etq.email,
        etq.questionnaire_data,
        etq.total_score,
        etq.max_score,
        etq.is_eligible,
        etq.status,
        etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM extend_tp_questionnaire etq
      LEFT JOIN patient p ON etq.patient_id = p."patientID"
      WHERE etq.status IN ('pending', 'submitted') AND etq.is_eligible = false

      UNION ALL

      SELECT
        'add_22_thc' as type,
        atq.id,
        atq.patient_id,
        atq.email,
        atq.questionnaire_data,
        atq.total_score,
        atq.max_score,
        atq.is_eligible,
        atq.status,
        atq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM add_22_thc_questionnaire atq
      LEFT JOIN patient p ON atq.patient_id = p."patientID"
      WHERE atq.status IN ('pending', 'submitted') AND atq.is_eligible = false

      UNION ALL

      SELECT
        'quantity_increase' as type,
        qiq.id,
        qiq.patient_id,
        qiq.email,
        qiq.questionnaire_data,
        qiq.total_score,
        qiq.max_score,
        qiq.is_eligible,
        qiq.status,
        qiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        qiq.strength_requests
      FROM quantity_increase_questionnaire qiq
      LEFT JOIN patient p ON qiq.patient_id = p."patientID"
      WHERE qiq.status IN ('pending', 'submitted') AND qiq.is_eligible = false

      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const result = await client.query(query, [limitNum, offset]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', {
        requests: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get pending requests for admin view (reuses existing logic)
 */
export const getAdminPendingRequests: RequestHandler = catchAll(async (req, res) => {
  const { limit = '20', page = '1' } = req.query;
  const client = await db.connect();

  try {
    const limitNum = parseInt(limit as string);
    const pageNum = parseInt(page as string);
    const offset = (pageNum - 1) * limitNum;

    // First get the total count
    const countQuery = `
      SELECT COUNT(*) as total FROM (
        SELECT id FROM thc_increase_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = true
        UNION ALL
        SELECT id FROM extend_tp_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = true
        UNION ALL
        SELECT id FROM add_22_thc_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = true
        UNION ALL
        SELECT id FROM quantity_increase_questionnaire WHERE status IN ('pending', 'submitted') AND is_eligible = true
      ) combined_requests
    `;

    const countResult = await client.query(countQuery);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limitNum);

    const query = `
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.patient_id,
        tiq.email,
        tiq.questionnaire_data,
        tiq.total_score,
        tiq.max_score,
        tiq.is_eligible,
        tiq.status,
        tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM thc_increase_questionnaire tiq
      LEFT JOIN patient p ON tiq.patient_id = p."patientID"
      WHERE tiq.status IN ('pending', 'submitted') AND tiq.is_eligible = true

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.patient_id,
        etq.email,
        etq.questionnaire_data,
        etq.total_score,
        etq.max_score,
        etq.is_eligible,
        etq.status,
        etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM extend_tp_questionnaire etq
      LEFT JOIN patient p ON etq.patient_id = p."patientID"
      WHERE etq.status IN ('pending', 'submitted') AND etq.is_eligible = true

      UNION ALL

      SELECT
        'add_22_thc' as type,
        atq.id,
        atq.patient_id,
        atq.email,
        atq.questionnaire_data,
        atq.total_score,
        atq.max_score,
        atq.is_eligible,
        atq.status,
        atq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        NULL::jsonb as strength_requests
      FROM add_22_thc_questionnaire atq
      LEFT JOIN patient p ON atq.patient_id = p."patientID"
      WHERE atq.status IN ('pending', 'submitted') AND atq.is_eligible = true

      UNION ALL

      SELECT
        'quantity_increase' as type,
        qiq.id,
        qiq.patient_id,
        qiq.email,
        qiq.questionnaire_data,
        qiq.total_score,
        qiq.max_score,
        qiq.is_eligible,
        qiq.status,
        qiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        p."fullName" as patient_name,
        p.dob as patient_dob,
        qiq.strength_requests
      FROM quantity_increase_questionnaire qiq
      LEFT JOIN patient p ON qiq.patient_id = p."patientID"
      WHERE qiq.status IN ('pending', 'submitted') AND qiq.is_eligible = true

      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const result = await client.query(query, [limitNum, offset]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', {
        requests: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get processed requests (approved/rejected) for admin view
 */
export const getAdminProcessedRequests: RequestHandler = catchAll(async (req, res) => {
  const { status, type, limit = '50', page = '1' } = req.query;
  const client = await db.connect();

  try {
    const limitNum = parseInt(limit as string);
    const pageNum = parseInt(page as string);
    const offset = (pageNum - 1) * limitNum;

    // First get the total count
    let countQuery = '';
    if (type === 'thc_increase') {
      let tiqStatusFilter = '';
      if (status === 'approved' || status === 'rejected') {
        tiqStatusFilter = `AND tiq.status = '${status}'`;
      } else {
        tiqStatusFilter = `AND tiq.status IN ('approved', 'rejected')`;
      }
      countQuery = `
        SELECT COUNT(*) as total FROM thc_increase_questionnaire tiq
        WHERE tiq.status IN ('approved', 'rejected') ${tiqStatusFilter}
      `;
    } else if (type === 'extend_tp') {
      let etqStatusFilter = '';
      if (status === 'approved' || status === 'rejected') {
        etqStatusFilter = `AND etq.status = '${status}'`;
      } else {
        etqStatusFilter = `AND etq.status IN ('approved', 'rejected')`;
      }
      countQuery = `
        SELECT COUNT(*) as total FROM extend_tp_questionnaire etq
        WHERE etq.status IN ('approved', 'rejected') ${etqStatusFilter}
      `;
    } else if (type === 'add_22_thc') {
      let atqStatusFilter = '';
      if (status === 'approved' || status === 'rejected') {
        atqStatusFilter = `AND atq.status = '${status}'`;
      } else {
        atqStatusFilter = `AND atq.status IN ('approved', 'rejected')`;
      }
      countQuery = `
        SELECT COUNT(*) as total FROM add_22_thc_questionnaire atq
        WHERE atq.status IN ('approved', 'rejected') ${atqStatusFilter}
      `;
    } else if (type === 'quantity_increase') {
      let qiqStatusFilter = '';
      if (status === 'approved' || status === 'rejected') {
        qiqStatusFilter = `AND qiq.status = '${status}'`;
      } else {
        qiqStatusFilter = `AND qiq.status IN ('approved', 'rejected')`;
      }
      countQuery = `
        SELECT COUNT(*) as total FROM quantity_increase_questionnaire qiq
        WHERE qiq.status IN ('approved', 'rejected') ${qiqStatusFilter}
      `;
    } else {
      // All types
      let baseStatusFilter = '';
      if (status === 'approved' || status === 'rejected') {
        baseStatusFilter = `AND status = '${status}'`;
      } else {
        baseStatusFilter = `AND status IN ('approved', 'rejected')`;
      }
      countQuery = `
        SELECT COUNT(*) as total FROM (
          SELECT id FROM thc_increase_questionnaire WHERE status IN ('approved', 'rejected') ${baseStatusFilter}
          UNION ALL
          SELECT id FROM extend_tp_questionnaire WHERE status IN ('approved', 'rejected') ${baseStatusFilter}
          UNION ALL
          SELECT id FROM add_22_thc_questionnaire WHERE status IN ('approved', 'rejected') ${baseStatusFilter}
          UNION ALL
          SELECT id FROM quantity_increase_questionnaire WHERE status IN ('approved', 'rejected') ${baseStatusFilter}
        ) combined_requests
      `;
    }

    // Now create the statusFilter for the main query
    let statusFilter = '';
    if (status === 'approved' || status === 'rejected') {
      statusFilter = `AND tiq.status = '${status}'`;
    } else {
      statusFilter = `AND tiq.status IN ('approved', 'rejected')`;
    }

    const countResult = await client.query(countQuery);
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limitNum);

    let query = '';

    if (type === 'thc_increase') {
      // Only THC increase requests
      query = `
        SELECT
          'thc_increase' as type,
          tiq.id,
          tiq.patient_id,
          tiq.email,
          tiq.questionnaire_data,
          tiq.total_score,
          tiq.max_score,
          tiq.status,
          tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(tiq.approved_at, tiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(tiq.approved_by, tiq.reviewed_by) as reviewed_by,
          tiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM thc_increase_questionnaire tiq
        LEFT JOIN patient p ON tiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(tiq.approved_by, tiq.reviewed_by) = d."accessID"
        WHERE tiq.status IN ('approved', 'rejected') ${statusFilter}
        ORDER BY COALESCE(tiq.approved_at, tiq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else if (type === 'extend_tp') {
      // Only extend TP requests
      query = `
        SELECT
          'extend_tp' as type,
          etq.id,
          etq.patient_id,
          etq.email,
          etq.questionnaire_data,
          etq.total_score,
          etq.max_score,
          etq.status,
          etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(etq.approved_at, etq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(etq.approved_by, etq.reviewed_by) as reviewed_by,
          etq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM extend_tp_questionnaire etq
        LEFT JOIN patient p ON etq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(etq.approved_by, etq.reviewed_by) = d."accessID"
        WHERE etq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'etq.status')}
        ORDER BY COALESCE(etq.approved_at, etq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else if (type === 'add_22_thc') {
      // Only add 22% THC requests
      query = `
        SELECT
          'add_22_thc' as type,
          atq.id,
          atq.patient_id,
          atq.email,
          atq.questionnaire_data,
          atq.total_score,
          atq.max_score,
          atq.status,
          atq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(atq.approved_at, atq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(atq.approved_by, atq.reviewed_by) as reviewed_by,
          atq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM add_22_thc_questionnaire atq
        LEFT JOIN patient p ON atq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(atq.approved_by, atq.reviewed_by) = d."accessID"
        WHERE atq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'atq.status')}
        ORDER BY COALESCE(atq.approved_at, atq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else if (type === 'quantity_increase') {
      // Only quantity increase requests
      query = `
        SELECT
          'quantity_increase' as type,
          qiq.id,
          qiq.patient_id,
          qiq.email,
          qiq.questionnaire_data,
          qiq.total_score,
          qiq.max_score,
          qiq.status,
          qiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(qiq.approved_at, qiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(qiq.approved_by, qiq.reviewed_by) as reviewed_by,
          qiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          qiq.strength_requests
        FROM quantity_increase_questionnaire qiq
        LEFT JOIN patient p ON qiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(qiq.approved_by, qiq.reviewed_by) = d."accessID"
        WHERE qiq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'qiq.status')}
        ORDER BY COALESCE(qiq.approved_at, qiq.reviewed_at) DESC
        LIMIT $1 OFFSET $2
      `;
    } else {
      // All types (default)
      query = `
        SELECT
          'thc_increase' as type,
          tiq.id,
          tiq.patient_id,
          tiq.email,
          tiq.questionnaire_data,
          tiq.total_score,
          tiq.max_score,
          tiq.status,
          tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(tiq.approved_at, tiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(tiq.approved_by, tiq.reviewed_by) as reviewed_by,
          tiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM thc_increase_questionnaire tiq
        LEFT JOIN patient p ON tiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(tiq.approved_by, tiq.reviewed_by) = d."accessID"
        WHERE tiq.status IN ('approved', 'rejected') ${statusFilter}

        UNION ALL

        SELECT
          'extend_tp' as type,
          etq.id,
          etq.patient_id,
          etq.email,
          etq.questionnaire_data,
          etq.total_score,
          etq.max_score,
          etq.status,
          etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(etq.approved_at, etq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(etq.approved_by, etq.reviewed_by) as reviewed_by,
          etq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM extend_tp_questionnaire etq
        LEFT JOIN patient p ON etq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(etq.approved_by, etq.reviewed_by) = d."accessID"
        WHERE etq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'etq.status')}

        UNION ALL

        SELECT
          'add_22_thc' as type,
          atq.id,
          atq.patient_id,
          atq.email,
          atq.questionnaire_data,
          atq.total_score,
          atq.max_score,
          atq.status,
          atq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(atq.approved_at, atq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(atq.approved_by, atq.reviewed_by) as reviewed_by,
          atq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          NULL::jsonb as strength_requests
        FROM add_22_thc_questionnaire atq
        LEFT JOIN patient p ON atq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(atq.approved_by, atq.reviewed_by) = d."accessID"
        WHERE atq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'atq.status')}

        UNION ALL

        SELECT
          'quantity_increase' as type,
          qiq.id,
          qiq.patient_id,
          qiq.email,
          qiq.questionnaire_data,
          qiq.total_score,
          qiq.max_score,
          qiq.status,
          qiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
          COALESCE(qiq.approved_at, qiq.reviewed_at) AT TIME ZONE 'Australia/Sydney' as reviewed_at,
          COALESCE(qiq.approved_by, qiq.reviewed_by) as reviewed_by,
          qiq.review_notes,
          p."fullName" as patient_name,
          d.name as doctor_name,
          qiq.strength_requests
        FROM quantity_increase_questionnaire qiq
        LEFT JOIN patient p ON qiq.patient_id = p."patientID"
        LEFT JOIN Dr d ON COALESCE(qiq.approved_by, qiq.reviewed_by) = d."accessID"
        WHERE qiq.status IN ('approved', 'rejected') ${statusFilter.replace('tiq.status', 'qiq.status')}

        ORDER BY reviewed_at DESC
        LIMIT $1 OFFSET $2
      `;
    }

    const result = await client.query(query, [limitNum, offset]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', {
        requests: result.rows,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get request statistics for admin dashboard
 */
export const getAdminRequestStats: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    const query = `
      SELECT
        COUNT(*) FILTER (WHERE status IN ('pending', 'submitted') AND is_eligible = true) as pending,
        COUNT(*) FILTER (WHERE status = 'approved') as approved,
        COUNT(*) FILTER (WHERE status = 'rejected') as rejected,
        COUNT(*) FILTER (WHERE status IN ('pending', 'submitted') AND is_eligible = false) as non_eligible,
        COUNT(*) as total
      FROM (
        SELECT status, is_eligible FROM thc_increase_questionnaire
        UNION ALL
        SELECT status, is_eligible FROM extend_tp_questionnaire
        UNION ALL
        SELECT status, is_eligible FROM add_22_thc_questionnaire
        UNION ALL
        SELECT status, is_eligible FROM quantity_increase_questionnaire
      ) combined_requests
    `;

    const result = await client.query(query);
    const stats = result.rows[0];

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', {
        pending: parseInt(stats.pending),
        approved: parseInt(stats.approved),
        rejected: parseInt(stats.rejected),
        nonEligible: parseInt(stats.non_eligible),
        total: parseInt(stats.total)
      }, true)
    );
  } finally {
    client.release();
  }
});

/**
 * Get patient request history for admin lookup
 */
export const getAdminPatientRequestHistory: RequestHandler = catchAll(async (req, res) => {
  const { id: patientId } = req.params;
  const { limit = '20' } = req.query;
  const client = await db.connect();

  try {
    const query = `
      SELECT
        'thc_increase' as type,
        tiq.id,
        tiq.questionnaire_data,
        tiq.total_score,
        tiq.max_score,
        tiq.status,
        tiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        tiq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        tiq.approved_by as reviewed_by,
        tiq.review_notes,
        d.name as doctor_name,
        NULL::jsonb as strength_requests
      FROM thc_increase_questionnaire tiq
      LEFT JOIN Dr d ON tiq.approved_by = d."accessID"
      WHERE tiq.patient_id = $1

      UNION ALL

      SELECT
        'extend_tp' as type,
        etq.id,
        etq.questionnaire_data,
        etq.total_score,
        etq.max_score,
        etq.status,
        etq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        etq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        etq.approved_by as reviewed_by,
        etq.review_notes,
        d.name as doctor_name,
        NULL::jsonb as strength_requests
      FROM extend_tp_questionnaire etq
      LEFT JOIN Dr d ON etq.approved_by = d."accessID"
      WHERE etq.patient_id = $1

      UNION ALL

      SELECT
        'add_22_thc' as type,
        atq.id,
        atq.questionnaire_data,
        atq.total_score,
        atq.max_score,
        atq.status,
        atq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        atq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        atq.approved_by as reviewed_by,
        atq.review_notes,
        d.name as doctor_name,
        NULL::jsonb as strength_requests
      FROM add_22_thc_questionnaire atq
      LEFT JOIN Dr d ON atq.approved_by = d."accessID"
      WHERE atq.patient_id = $1

      UNION ALL

      SELECT
        'quantity_increase' as type,
        qiq.id,
        qiq.questionnaire_data,
        qiq.total_score,
        qiq.max_score,
        qiq.status,
        qiq.created_at AT TIME ZONE 'Australia/Sydney' as created_at,
        qiq.approved_at AT TIME ZONE 'Australia/Sydney' as reviewed_at,
        qiq.approved_by as reviewed_by,
        qiq.review_notes,
        d.name as doctor_name,
        qiq.strength_requests
      FROM quantity_increase_questionnaire qiq
      LEFT JOIN Dr d ON qiq.approved_by = d."accessID"
      WHERE qiq.patient_id = $1

      ORDER BY created_at DESC
      LIMIT $2
    `;

    const result = await client.query(query, [patientId, limit]);

    res.status(httpStatus.OK).json(
      new ApiResponse(httpStatus.OK, 'SUCCESS', { requests: result.rows }, true)
    );
  } finally {
    client.release();
  }
});
