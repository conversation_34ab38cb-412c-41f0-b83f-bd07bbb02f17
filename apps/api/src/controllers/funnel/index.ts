import axios, { AxiosError } from 'axios';
import { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import config from '../../config/index';
import { ZohoAuth, zohoLeadURL, zohoContactURL } from '../../helpers/zoho';
import { updateContactPatientRequest } from '../zoho';
import { AICheckerResponse, ConsentFormData, RegistrationData } from '../../types/funnel';
import { catchAll } from '../../utils/catchAll';
import FormData from 'form-data';
import httpStatus from 'http-status';
import { ApiError } from '../../utils/ApiError';
import { db } from '../../utils/db';
import { v4 as uuid } from 'uuid';
import Stripe from 'stripe';
import { UrlEncryptionHelper } from '../../helpers/encryption';
import OtpHelper from '../../helpers/otp';
import moment from 'moment';
import { logger } from '../../config/logger';
import { ApiResponse } from '../../helpers/response';

import { WordPressUtils } from '../../helpers/wordpress';
import { getStateFromIp } from '../../helpers/others';
import { UAParser } from 'ua-parser-js';
import { requestModerationService } from '../../services/requestModeration.service';

import aiChecker from './aiChecker';
import { PatientFormData } from '../../types';

const getCookie = (cookie: string | undefined) => {
  let cookies: { [key: string]: string } = {};

  if (cookie) {
    cookies = cookie.split('; ').reduce((acc, cookie) => {
      const [name, value] = cookie.split('=');
      acc[name] = value;
      return acc;
    }, {});
  }

  const leadId = cookies['lead_id'];
  return leadId;
};

export const registerPatient: RequestHandler = catchAll(async (req, res) => {
  const body = req.body as RegistrationData;
  let client;
  const patientFullName = `${body.firstname} ${body.lastname}`;

  // Get user's IP address from request
  const userIp = req.body.user_ip || req.ip || req.socket.remoteAddress || '';
  const headers = await ZohoAuth.getHeaders();
  body.phone = body.phone.replace(/\s/g, '');

  const data = {
    data: [
      {
        Email: body.email,
        Phone: body.phone,
        Mobile: body.phone,
        First_Name: body.firstname,
        Last_Name: body.lastname,
        Is_Stored_In_DB: 'yes',
        Member_Status: '1 - Reg Form Completed',
        User_IP: userIp, // Add the user's IP address to Zoho
      },
    ],
  };

  try {
    const state = await getStateFromIp(userIp);
    client = await db.connect();
    await client.query('BEGIN');
    const checkPatientByPhone = await client.query(`SELECT * from patient WHERE mobile=$1`, [body.phone.trim()]);
    if (checkPatientByPhone.rows && checkPatientByPhone.rows.length >= 1) {
      res.status(400).send({
        error: 'A patient with this phone number already exists. You need to login',
        type: 'phone',
      });
      return;
    }
    const checkPatient = `SELECT * from patient WHERE email=$1`;
    const existingPatient = await client.query(checkPatient, [body.email]);
    if (existingPatient.rows && existingPatient.rows.length >= 1) {
      res.status(400).send({
        error: 'A patient with this email already exists. You need to login',
        type: 'email',
      });
      return;
    }
    const zohoLeadFound = await ZohoAuth.getZohoLeadByEmail(body.email);
    let leadID;
    if (zohoLeadFound == null) {
      const result = await axios.post(`${zohoLeadURL}`, data, { headers });
      leadID = result.data.data?.[0].details?.id;
    } else {
      leadID = zohoLeadFound.data?.[0].id;
    }

    const query = `
    INSERT INTO patient
    ("fullName", email, "returningPatient", "zohoID", "patientID", password, "lastCompletedForm","mobile","state", "createdAt", "updatedAt")
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8,$9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

    await client.query(query, [
      patientFullName,
      body.email,
      false,
      leadID,
      leadID,
      body.password,
      'registration',
      body.phone,
      state,
    ]);
    const dataZoho = {
      data: [
        {
          State: state,
        },
      ],
    };
    await axios.put(`${zohoLeadURL}/${leadID}`, dataZoho, { headers });
    const otp = await OtpHelper.generateOtp(client, body.phone);
    let wpUser = await WordPressUtils.getWPUser(body.email.trim());
    if (wpUser == null) {
      const userData = {
        username: body.email.trim(),
        first_name: body.firstname.trim(),
        last_name: body.lastname.trim(),
        email: body.email.trim(),
        password: body.password,
        meta: {
          billing_phone: body.phone.trim(),
          shipping_phone: body.phone.trim(),
          _discharge_quiz: data,
          _zoho_crm_lead_id: leadID,
        },
      };
      wpUser = await WordPressUtils.createWPUser(userData);
    }
    await client.query('COMMIT');
    OtpHelper.sendOTP(body.phone, otp, leadID);
    res.status(200).send({ success: true, message: 'successfully registered', data: { leadID } });
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const validateSession: RequestHandler = catchAll(async (req, res) => {
  const cookie = req.headers.cookie;
  const leadID = getCookie(cookie);
  let client;

  if (leadID) {
    try {
      client = await db.connect();
      const checkPatient = `SELECT "fullName", "email","mobile" as phone,"phoneVerified" as phoneverified,"lastCompletedForm" as laststep, "applicationStatus" as status from patient WHERE "zohoID"=$1`;
      const existingPatient = await client.query(checkPatient, [leadID]);
      if (existingPatient.rows && existingPatient.rows.length > 0) {
        res.status(200).send({ authenticated: true, user: existingPatient.rows[0] });
        return;
      } else {
        res.status(403).send({ authenticated: false });
        return;
      }
    } catch (e) {
      console.error('Error validating user:', e);
      const error = e as AxiosError;
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    } finally {
      client.release();
    }
  }
  res.status(403).send({ authenticated: false });
  return;
});

export const validateUser: RequestHandler = catchAll(async (req, res, next) => {
  const cookie = req.headers.cookie;
  const leadID = getCookie(cookie);
  let client;

  if (!leadID) {
    res.status(403).send({ authenticated: false });
    return;
  }

  try {
    client = await db.connect();
    const checkPatient = `SELECT * from patient WHERE "zohoID"=$1`;
    const existingPatient = await client.query(checkPatient, [leadID]);

    if (existingPatient.rows && existingPatient.rows.length > 0) {
      if (!next) {
        res.status(200).send({ authenticated: true });
        return;
      }
      next();
      return;
    } else {
      res.status(403).send({ authenticated: false });
      return;
    }
  } catch (error) {
    console.error('Error validating user:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occurred during user validation');
  } finally {
    if (client) {
      client.release();
    }
  }
});

export const funnelLoginEmailPassword: RequestHandler = catchAll(async (req, res) => {
  const { email, password } = req.body;
  let client;
  const findPatient = `SELECT "fullName", "email","mobile" as phone,"phoneVerified" as phoneverified,"lastCompletedForm" as laststep,"zohoID", "applicationStatus" as status from patient WHERE ("email"=$1 OR "mobile"=$1) AND "password"=$2`;
  try {
    client = await db.connect();
    const existingPatient = await client.query(findPatient, [email, password]);
    if (existingPatient.rows && existingPatient.rows.length > 0) {
      const leadID = existingPatient.rows[0].zohoID;
      if (config.secureFunnelCookie) {
        res.cookie('lead_id', leadID, {
          httpOnly: true,
          sameSite: 'none',
          secure: true,
        });
      } else {
        res.cookie('lead_id', leadID, {
          httpOnly: true,
          secure: false,
        });
      }
      res.status(200).send({ authenticated: true, user: existingPatient.rows[0] });
      return;
    }
    res.status(400).send('Email or password incorrect');
  } finally {
    client.release();
  }
});

export const funnelLogin: RequestHandler = catchAll(async (req, res) => {
  const leadID = req.body.leadID as string;
  const client = await db.connect();
  const checkPatient = `SELECT "fullName", "email","mobile" as phone,"phoneVerified" as phoneverified,"lastCompletedForm" as laststep, "applicationStatus" as status from patient WHERE "zohoID"=$1`;
  const existingPatient = await client.query(checkPatient, [leadID]);
  try {
    if (existingPatient.rows && existingPatient.rows.length > 0) {
      if (config.secureFunnelCookie) {
        res.cookie('lead_id', leadID, {
          httpOnly: true,
          sameSite: 'none',
          secure: true,
        });
      } else {
        res.cookie('lead_id', leadID, {
          httpOnly: true,
          secure: false,
        });
      }
      res.status(200).send({ authenticated: true, user: existingPatient.rows[0] });
      return;
    }
    res.status(200).send({ authenticated: false });
  } finally {
    client.release();
  }
});

export const postQuestionnaire: RequestHandler = catchAll(async (req, res) => {
  const data = req.body as { [key: string]: string };
  const headers = await ZohoAuth.getHeaders();
  const questionnaireID = uuid();
  const cookie = req.headers.cookie;
  const leadId = getCookie(cookie);

  const questions = {
    dob: 'What is your date of birth?',
    gender: 'What was your Gender at Birth?',
    treatment:
      'Have you discussed other treatment options with your doctor? Including medical and conservative therapies.?',
    trial:
      'Knowing the alternative management options, do you still want to trial Alternative Medicine as a treatment option for your condition?',
    condition: 'What condition or symptom are you having issues with?',
    children: 'Are you planning to have children in the near future?',
    alternative_medecine: 'Have you used alternative medicine before?',
    first_medication: 'Please add the first medication, treatment or therapy you trialled.',
    second_medication: 'Please add the second medication, treatment or therapy you trialled.',
    disorder: 'Do you suffer from psychosis, bipolar disorder or schizophrenia?',
    diseases: 'Do you suffer from any cardiovascular diseases, including irregular heartbeat (arrhythmia)?',
    addiction:
      'Do you have an addiction to any psychoactive substances and/or drugs, including alcohol, but excluding nicotine and caffeine?',
  };

  const combined = Object.keys(questions).map((key) => ({
    question: questions[key],
    answer: data[key],
  }));

  const values: unknown[] = [];
  const placeholders: string[] = [];

  combined.forEach((item, index) => {
    const offset = index * 5;
    placeholders.push(
      `($${offset + 1}, $${offset + 2}, $${offset + 3}, $${offset + 4}, $${offset + 5}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
    );
    values.push(leadId, leadId, item.question, item.answer, questionnaireID);
  });

  const query = `
    INSERT INTO Questionnaire ("patientID", "zohoID", question, answers, "questionnaireID", "createdAt", "updatedAt")
    VALUES ${placeholders.join(', ')}
    ;
  `;

  const client = await db.connect();

  const dataZoho = {
    data: [
      {
        What_condition_or_symptom_are_you_having_issues_wi: data.condition,
        Are_you_planning_to_have_children_in_the_near_futu: data.children,
        Do_you_suffer_from_psychosis_bipolar_disorder_or: data.disorder,
        Do_you_suffer_from_any_cardiovascular_diseases_in: data.diseases,
        Do_you_have_an_addiction_to_any_psychoactive_subst: data.addiction,
        User_Date_Of_Birth: data.dob,
        Date_of_Birth_2: data.dob,
        Have_you_used_Alternative_Medicines_before_whethe: data.alternative_medecine,
        Please_add_the_first_medication_treatment_or_ther: data.first_medication,
        Please_add_the_second_medication_treatment_or_the: data.second_medication,
        Member_Status: '2 - Questionnaire Completed',
        Knowing_the_alternative_management_options_do_you: data.trial,
        Have_you_discussed_other_treatment_options_with_yo: data.treatment,
        What_was_your_gender_at_birth: data.gender,
        Lead_Status: '',
        AI_Feedback_Pre_Screening: '',
      },
    ],
  };

  try {
    await client.query('BEGIN');
    await client.query(query, values);

    const result = await validatePatient(data as PatientFormData, leadId);
    let applicationStatus = 'approved';
    if (!result.isValid) {
      dataZoho.data[0].Lead_Status = 'Junk Lead';
      applicationStatus = 'rejected';
    } else if (result.softReject) {
      dataZoho.data[0].Lead_Status = 'Pending Review';
      applicationStatus = 'pending';
    }
    // Update the AI feedback in the Zoho CRM
    dataZoho.data[0].AI_Feedback_Pre_Screening =
      result.aiResponse +
      '\n' +
      result.aiExplanation +
      '\n' +
      `https://${process.env.DR_UI_URL}/patient/report/${leadId}`;
    // Log the AI response into the database
    await client.query(
      `
      UPDATE PATIENT
      SET "lastCompletedForm"=$1,"dob"=$2, "updatedAt" = CURRENT_TIMESTAMP, "applicationStatus" = $3
      WHERE "zohoID"=$4`,
      ['questionnaire', data.dob, applicationStatus, leadId],
    );

    // const result = await validatePatient(data);
    // if (!result.isValid) {
    //   dataZoho.data[0].Lead_Status = 'Junk Lead';

    // }

    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
    await client.query('COMMIT');
    res.status(200).send(result);
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updateApplicationStatus: RequestHandler = catchAll(async (req, res) => {
  const { leadId, status } = req.body;
  // If leadId and status are not provided, return an error
  if (!leadId || !status) {
    res.status(400).send({
      error: 'leadId and status are required',
    });
    return;
  }
  // Update the application status in the database
  const client = await db.connect();
  try {
    await client.query('BEGIN');
    const query = `
      UPDATE patient
      SET "applicationStatus" = $1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "zohoID" = $2
    `;
    await client.query(query, [status.toLowerCase(), leadId]);
    await client.query('COMMIT');
    res.status(200).send({ success: true, message: 'Application status updated successfully' });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating application status:', error);
    res.status(500).send({ message: 'An error occurred while updating the application status' });
  }
});

const filterKeywords = [
  // Slurs and offensive words (non-exhaustive, censoring for safety)
  'retard',
  'idiot',
  'dumb',
  'crazy',
  'loony',
  'moron',
  'psycho',
  'nuts',
  'junkie',
  'crackhead',
  'addict',
  'druggie',

  // Ineligible conditions or high-risk psychiatric terms
  'schizophrenia',
  'psychosis',
  'paranoia',
  'delusional disorder',
  'hallucination',
  'bipolar',
  'manic episode',
  'mental breakdown',

  // Lazy/low-effort terms
  'nah',
  'nuh uh',
  'idk',
  'zzz',
  'nope',
  'none',
  'nothing',
  'whatever',
  'naaa',
  'lol',
  'help',
  'pls',
  'please help',
  'asdf',
  'qwerty',

  // Drug/recreational slang or intent
  'high',
  'stoned',
  'blazed',
  'getting high',
  'recreational',
  '420',
  'weed',
  'pot',
  'just for fun',
  'smoke up',
  'hit a blunt',
];

export const validatePatient = async (data: PatientFormData, leadId: string) => {
  const errors: string[] = [];
  let softReject = false;
  let ai_response: AICheckerResponse | undefined;

  // 1. Age Validation (≥20 years old)
  const dob = new Date(data.dob);
  const age = new Date().getFullYear() - dob.getFullYear();
  if (age < 20) {
    errors.push('Patient must be at least 20 years old');
  }
  // Check whether the condition and medications contain inappropriate terms
  if (
    !filterKeywords.some((keyword) => data.condition.toLowerCase().includes(keyword)) &&
    !filterKeywords.some((keyword) => data.first_medication.toLowerCase().includes(keyword)) &&
    !filterKeywords.some((keyword) => data.second_medication.toLowerCase().includes(keyword))
  ) {
    // 2. AI Checker
    // Call the AI checker to validate the condition and medications
    const aiResponse: AICheckerResponse = await aiChecker(
      data.condition,
      data.first_medication,
      data.second_medication,
      data.children,
      data.disorder,
      data.diseases,
    );

    // Log the AI response for debugging
    logger.info('AI Response:', aiResponse);
    // Check if the AI response is valid
    if (!aiResponse || !aiResponse.condition || !aiResponse.tried_medications || !aiResponse.response) {
      softReject = true;
    } else {
      // Log the AI response into the database
      const client = await db.connect();
      await client.query(
        `
      INSERT INTO AICheckResponses (lead_id,condition, tried_medications, response, explanation, "createdAt", pregnancy, psychotic_disorder, cardiovascular_diseases, risk_score)
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, $6, $7, $8, $9)
      `,
        [
          leadId,
          aiResponse.condition,
          aiResponse.tried_medications,
          aiResponse.response,
          aiResponse.explanation,
          aiResponse.pregnancy,
          aiResponse.psychotic_disorder,
          aiResponse.cardiovascular_diseases,
          aiResponse.risk_score,
        ],
      );
      ai_response = aiResponse;
      client.release();
    }
    // Check If the AI rejected the condition or medications
    if (aiResponse.response === 'NO') {
      softReject = true;
    }
  } else {
    errors.push('Condition or medications contain inappropriate terms');
  }

  // 4. Business Rule Validations
  if (data.children.toLowerCase() === 'yes') {
    errors.push('Cannot plan to have children in next 6 months');
  }

  if (data.disorder.toLowerCase() === 'yes') {
    errors.push('Cannot have psychosis/bipolar disorder/schizophrenia');
  }

  if (data.diseases.toLowerCase() !== 'no') {
    // Cardiovascular disease check
    const disqualifyingConditions = [
      'unstable',
      'untreated',
      'arrhythmia',
      'aneurysm',
      'uncontrolled',
      'cerebrovascular',
      'heart failure',
      'post-mi-treated-disqualify',
    ];

    if (disqualifyingConditions.some((term) => data.diseases.toLowerCase().includes(term))) {
      errors.push('Disqualified due to cardiovascular condition');
    }
  }

  if (data.addiction.toLowerCase() === 'yes') {
    errors.push('Cannot have substance addiction');
  }

  // if (data.treatment.toLowerCase() === 'no') {
  //   errors.push('Must be interested in alternative treatment');
  // }

  // if (data.alternative_medecine.toLowerCase() === 'no') {
  //   errors.push('Must have prior alternative medicine use');
  // }

  if (data.trial.toLowerCase() === 'no') {
    errors.push('Must want to trial alternative medicine');
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
    softReject,
    aiResponse: ai_response?.response ?? 'No response',
    aiExplanation: ai_response?.explanation ?? 'No explanation',
  };
};

export const postDischargeLetter: RequestHandler = catchAll(async (req, res) => {
  const headers = await ZohoAuth.getHeaders();
  const client = await db.connect();
  const cookie = req.headers.cookie;
  const leadId = getCookie(cookie);
  try {
    await client.query('BEGIN');
    const letter = req.files?.[0];
    const formData = new FormData();
    formData.append('file', letter.buffer, {
      filename: letter.originalname,
      contentType: letter.mimetype,
    });

    const config = {
      headers: {
        ...headers,
        ...formData.getHeaders(),
      },
    };

    const query = `
      INSERT INTO DischargeLetters ("fileName", "mimeType", "patientID", "zohoID", "fileData", "createdAt")
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    `;

    await client.query(query, [letter.originalname, letter.mimetype, leadId, leadId, letter.buffer]);

    //get the member status of the lead from zoho by email

    const zohoLeadResponse = await ZohoAuth.getZohoLeadById(leadId);

    // Extract and trim the member status to remove any potential whitespace
    const memberStatus = zohoLeadResponse?.data[0]?.Member_Status?.trim();
    const targetStatus = '9b - Approve Subject To Discharge';

    // Only run the query if the status is NOT equal to "9b - Approve Subject To Discharge"
    if (memberStatus !== targetStatus) {
      await client.query(
        `
        UPDATE PATIENT
        SET "lastCompletedForm"=$1, "updatedAt" = CURRENT_TIMESTAMP
        WHERE "zohoID"=$2`,
        ['discharge', leadId],
      );
    }

    const dataZoho = {
      data: [
        {
          Discharge_Letter_Form: 'Uploaded',
          ...(memberStatus !== targetStatus ? { Member_Status: '4 - Discharge Letter Form' } : {}),
        },
      ],
    };

    await axios.post(`${zohoLeadURL}/${leadId}/Attachments`, formData, config);
    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });

    await client.query('COMMIT');
    res.status(200).send({ success: true, message: 'successfully updated lead' });
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const postDischargeLetterstatus: RequestHandler = catchAll(async (req, res) => {
  const headers = await ZohoAuth.getHeaders();
  const client = await db.connect();
  const cookie = req.headers.cookie;
  const leadId = getCookie(cookie);
  const status = req.body.status;
  try {
    await client.query('BEGIN');
    const query = `
      INSERT INTO DischargeLetters ("patientID", "zohoID", status, "createdAt")
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
    `;

    const dataZoho = {
      data: [
        {
          Member_Status: '4 - Discharge Letter Form',
        },
      ],
    };

    await client.query(query, [leadId, leadId, status]);
    await client.query(
      `
      UPDATE PATIENT
      SET "lastCompletedForm"=$1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "zohoID"=$2`,
      ['discharge', leadId],
    );
    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });

    await client.query('COMMIT');
    res.status(200).send({ success: true, message: 'successfully updated lead' });
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const createPaymentIntent: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  try {
    await client.query('BEGIN');
    const user = req.body as RegistrationData;
    const ApiKey = user?.fullName?.includes('TESTX') ? config.stipeTestApiKey : config.stripeApiKey;
    const stripe = new Stripe(ApiKey);
    const headers = await ZohoAuth.getHeaders();
    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: 2999, // $29.99 in cents
      currency: 'aud',
      description: `Lead: ${leadId}, Reason: Consult Hold Fee`,
      payment_method_types: ['card'],
      capture_method: 'manual',
    });

    const dataZoho = {
      data: [
        {
          Member_Status: '6 - Consult Hold Fee',
        },
      ],
    };

    await client.query(
      `
      UPDATE PATIENT
      SET "lastCompletedForm"=$1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "zohoID"=$2`,
      ['fees', leadId],
    );

    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
    await client.query('COMMIT');

    res.status(200).send({ success: true, data: { clientSecret: paymentIntent.client_secret } });
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error creating payment intent', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// Unused
export const captureStripePayment: RequestHandler = catchAll(async (req, res) => {
  const { paymentIntentId, user } = req.body;
  try {
    const ApiKey = user?.fullName?.includes('TESTX') ? config.stipeTestApiKey : config.stripeApiKey;
    const stripe = new Stripe(ApiKey);
    const paymentIntent = await stripe.paymentIntents.capture(paymentIntentId);
    res.json({ paymentIntent });
  } catch (e) {
    console.error('Error capturing payment', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  }
});

// Unused
export const cancelStripePayment: RequestHandler = catchAll(async (req, res) => {
  const { paymentIntentId, user } = req.body;
  // GET ID from DB
  try {
    const ApiKey = user?.fullName?.includes('TESTX') ? config.stipeTestApiKey : config.stripeApiKey;
    const stripe = new Stripe(ApiKey);
    const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);
    res.json({ paymentIntent });
  } catch (e) {
    console.error('Error capturing payment', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  }
});

export const encryptURL: RequestHandler = catchAll(async (req, res) => {
  const cookie = req.headers.cookie;
  const leadID = getCookie(cookie);
  try {
    if (leadID) {
      const urlId = UrlEncryptionHelper.encryptLeadId(leadID);
      res.status(200).send({ success: true, data: { leadID: urlId } });
      return;
    }
    res.status(500).send({ leadID: undefined });
  } catch (error) {
    const e = error as Error;
    res.status(500).send({ leadID: undefined });
    throw new ApiError(httpStatus.BAD_REQUEST, e.message);
  }
});

export const postHealthCheckSurvey: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const user = body.user;
  const client = await db.connect();
  const healthCheckID = uuid();

  const data = body.data as {
    question: string;
    answer: string;
  }[];

  const values: unknown[] = [];
  const placeholders: string[] = [];

  try {
    await client.query('BEGIN');

    const existingUser = await client.query(`SELECT * FROM patient WHERE email=$1`, [user]);

    if (existingUser.rows?.[0].email) {
      const email = existingUser.rows?.[0].email;
      data.forEach((item, index) => {
        const offset = index * 5;
        placeholders.push(
          `($${offset + 1}, $${offset + 2}, $${offset + 3}, $${offset + 4}, $${offset + 5}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        );
        values.push(item.question, email, item.answer, healthCheckID, 'healthcheck');
      });

      const query = `
      INSERT INTO healthcheck (question, "email", answers, "healthCheckID", "type", "createdAt", "updatedAt")
      VALUES ${placeholders.join(', ')}
      ;
    `;

      await client.query(query, values);
      await client.query('COMMIT');

      res.status(200).send({ success: true, data: {} });
    } else {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Patient does not exist');
    }
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const createPrescriptionLead: RequestHandler = catchAll(async (req, res) => {
  const { patientId, status } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Get patient details from DB
    const checkPatient = `SELECT * from patient WHERE "patientID"=$1`;
    const existingPatient = await client.query(checkPatient, [patientId]);

    if (!existingPatient.rows || existingPatient.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Patient not found');
    }

    const patient = existingPatient.rows[0];
    const [firstName, ...lastNameParts] = patient.fullName.split(' ');
    const lastName = lastNameParts.join(' ');

    // Create new lead in Zoho CRM
    const headers = await ZohoAuth.getHeaders();
    const data = {
      data: [
        {
          Email: patient.email,
          Phone: patient.phone,
          Mobile: patient.phone,
          First_Name: firstName,
          Existing_Patient: 'yes',
          Last_Name: lastName,
          Is_Stored_In_DB: 'yes',
          Lead_Status: status === 'rejected' ? '10 - Doctor Rejected' : 'Pre-Qualified',
        },
      ],
    };

    const result = await axios.post(`${zohoLeadURL}`, data, { headers });
    const leadId = result.data.data?.[0].details?.id;

    logger.info(`Lead ID: ${leadId}`);

    // Update only the zohoID in the patient record
    await client.query(
      `
      UPDATE patient
      SET "zohoID" = $1, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $2
      `,
      [leadId, patient.patientID],
    );

    await client.query('COMMIT');
    res.status(200).send({
      success: true,
      message: 'Successfully created prescription lead',
      data: { leadId },
    });
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error creating prescription lead:', e);

    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const checkOtp: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const cookie = req.headers.cookie;
  const leadId = getCookie(cookie);
  const { otp } = body;
  let client;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    const existingOtp = await client.query(
      `SELECT * FROM otps WHERE otp=$1 and active=$2 and validated=$3 and context=$4`,
      [otp, true, false, 'register'],
    );
    if (existingOtp.rows && existingOtp.rows.length == 1) {
      const otpData = existingOtp.rows[0];
      if (!moment(otpData.expirationdate).isAfter(moment())) {
        res.status(400).send('OTP code expired');
        return;
      }
      await client.query(`UPDATE otps set "validated"=$1 WHERE otp=$2`, [true, otp]);
      await client.query(`UPDATE patient set "phoneVerified"=$1 WHERE "zohoID"=$2`, [true, leadId]);
      const data = {
        data: [
          {
            OTP_Validated: otp,
          },
        ],
      };
      const headers = await ZohoAuth.getHeaders();
      await axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });
      await client.query('COMMIT');
      res.status(200).send({ success: true, data: {} });
      return;
    } else {
      res.status(400).send('OTP code is incorrect');
    }
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('checkin otp :', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const resetOtpPasswordProcess: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const { otp, password, phone, email } = body;
  let client;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    const existingPatientWithEmail = await client.query(`SELECT * FROM patient WHERE email=$1`, [email]);
    if (existingPatientWithEmail.rows && existingPatientWithEmail.rows.length == 1) {
      const patientData = existingPatientWithEmail.rows[0];
      if (!patientData.mobile) {
        res.status(400).send('This account has no phone number related, please contact us');
        return;
      }
      if (patientData.mobile != phone) {
        res.status(400).send('This account phone number setup is not correct, please contact us');
        return;
      }
      if (!patientData.zohoID) {
        res.status(400).send('This account setup is not correct, please contact us');
        return;
      }
      const existingOtp = await client.query(
        `SELECT * FROM otps WHERE otp=$1 and active=$2 and validated=$3 and context=$4 and phone=$5`,
        [otp, true, false, 'resetpassword', phone],
      );
      if (existingOtp.rows && existingOtp.rows.length == 1) {
        const otpData = existingOtp.rows[0];
        if (!moment(otpData.expirationdate).isAfter(moment())) {
          res.status(400).send('OTP code expired');
          return;
        }
        await client.query(`UPDATE otps set "validated"=$1 WHERE otp=$2 and phone=$3`, [true, otp, phone]);
        await client.query(`UPDATE patient set "password"=$1 WHERE "email"=$2`, [password, email]);
        const wpUser = await WordPressUtils.getWPUser(email);
        if (wpUser != null) {
          const data = {
            password: password,
          };
          await WordPressUtils.updateWPUser(wpUser.id, data);
        }
        await client.query('COMMIT');
        res.status(200).send({ success: true, data: {} });
        return;
      } else {
        res.status(400).send('OTP code is incorrect');
      }
    } else {
      res.status(400).send('No patient found with this email');
    }
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('checkin otp :', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const resendOtp: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const cookie = req.headers.cookie;
  const leadId = getCookie(cookie);
  const { phone } = body;
  let client;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    await client.query(`UPDATE patient set "mobile"=$1 WHERE "zohoID"=$2`, [phone, leadId]);
    await OtpHelper.disablePhoneOtp(client, phone);
    const otp = await OtpHelper.generateOtp(client, phone);
    await OtpHelper.sendOTP(phone, otp, leadId);
    await client.query('COMMIT');
    res.status(200).send({ success: true, data: {} });
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('checkin otp :', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const forgotPassword: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const { phone } = body;
  let client;
  try {
    client = await db.connect();
    let email;
    let leadID;
    await client.query('BEGIN');
    const existingPatientWithPhone = await client.query(`SELECT * FROM patient WHERE mobile=$1`, [phone]);
    if (existingPatientWithPhone.rows && existingPatientWithPhone.rows.length == 1) {
      const patientData = existingPatientWithPhone.rows[0];
      if (!patientData.email) {
        res.status(400).send('This account has no email related, please contact us');
        return;
      }
      if (!patientData.zohoID) {
        res.status(400).send('This account setup is not correct, please contact us');
        return;
      }
      email = patientData.email;
      leadID = patientData.zohoID;
    } else {
      res.status(400).send('No patient found with this email');
    }
    await OtpHelper.disablePhoneOtp(client, phone);
    const otp = await OtpHelper.generateOtp(client, phone, 4, true);
    await OtpHelper.sendOTP(phone, otp, leadID);
    await client.query('COMMIT');
    res.status(200).send({ success: true, email });
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('checkin otp reset :', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const resendOtpReset: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  let leadID;
  const { email } = body;
  const { phone } = body;
  let client;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    const existingPatientWithEmail = await client.query(`SELECT * FROM patient WHERE email=$1`, [email]);
    if (existingPatientWithEmail.rows && existingPatientWithEmail.rows.length == 1) {
      const patientData = existingPatientWithEmail.rows[0];
      if (!patientData.mobile) {
        res.status(400).send('This account has no phone number related, please contact us');
        return;
      }
      if (patientData.mobile != phone) {
        res.status(400).send('This account phone number setup is not correct, please contact us');
        return;
      }
      if (!patientData.zohoID) {
        res.status(400).send('This account setup is not correct, please contact us');
        return;
      }
      leadID = patientData.zohoID;
    } else {
      res.status(400).send('No patient found with this email');
    }
    await OtpHelper.disablePhoneOtp(client, phone);
    const otp = await OtpHelper.generateOtp(client, phone, 4, true);
    await OtpHelper.sendOTP(phone, otp, leadID);
    await client.query('COMMIT');
    res.status(200).send({ success: true, data: {} });
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('checkin otp :', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const getPatientByleadID: RequestHandler = catchAll(async (req, res) => {
  let client;
  const leadId = req.params.leadId;

  const getPatientByZooId = `SELECT "fullName", "email","mobile" as phone,"zohoID" FROM patient where "zohoID"=$1`;

  try {
    client = await db.connect();
    const result = await client.query(getPatientByZooId, [leadId]);
    if (result.rows && result.rows.length > 0) {
      res.status(200).send({ authenticated: true, user: result.rows[0] });
      return;
    } else {
      res.status(400).send('No patient found, your link may be no more valid');
      return;
    }
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const getZohoLeadById: RequestHandler = catchAll(async (req, res) => {
  const leadId = req.params.leadId;

  try {
    const result = await ZohoAuth.getZohoLeadById(leadId);
    if (result == null) {
      res.status(204).send();
    } else {
      res.status(200).send(result);
    }
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  }
});

export const postPaymentSteps: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const step = body.message;
  const orderid = body.orderid;
  let client;
  const paymentLink = body.paymentLink;

  try {
    client = await db.connect();
    const userAgentString = req.headers['user-agent'];
    const parser = new UAParser(userAgentString);

    const os = parser.getOS().name;
    const browser = parser.getBrowser().name;
    const version = parser.getBrowser().version;
    const query = `
    INSERT INTO paymentflowlogs
    ("state", "os", "browser","creationDate","paymentLink","browserVersion", "orderId")
    VALUES ($1, $2, $3, CURRENT_TIMESTAMP,$4,$5,$6)
    `;

    await client.query(query, [step, os, browser, paymentLink, version, orderid]);
    const result = await ZohoAuth.updateDealsPaymentSteps(step, orderid);
    if (result == null) {
      res.status(204).send();
    } else {
      res.status(200).send(result);
    }
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

/**
 * Get the last consultation for a patient by zohoId
 * This endpoint returns the most recent consultation record for a patient
 */
export const getLastConsultationByZohoId: RequestHandler = catchAll(async (req, res) => {
  const zohoId = req.params.zohoId;
  let client: import('pg').PoolClient | undefined;
  const timeZone = "AT TIME ZONE 'Australia/Sydney'";

  try {
    client = await db.connect();

    // First, get the patientID from the zohoID
    const patientQuery = `SELECT "patientID" FROM Patient WHERE "zohoID" = $1`;
    const patientResult = await client.query(patientQuery, [zohoId]);

    if (patientResult.rows.length === 0) {
      res.status(404).send(new ApiResponse(httpStatus.NOT_FOUND, 'Patient not found', null, false, true));
      return;
    }

    const patientID = patientResult.rows[0].patientID;

    // Get the latest non-completed consultation for this patient
    const consultationQuery = `
      SELECT
        c.id,
        c."patientID",
        c."drId",
        c.email,
        TO_CHAR((c."joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
        TO_CHAR((c."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        c."meetingOngoing",
        c."drJoined",
        TO_CHAR((c."consultationStart" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationStart",
        TO_CHAR((c."consultationEnd" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationEnd",
        c."notificationSent",
        TO_CHAR((c."notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
        c.completed,
        c."queueTag",
        TO_CHAR((c."createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        TO_CHAR((c."updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt"
      FROM Consultation c
      WHERE c."patientID" = $1 AND c.completed = false
      ORDER BY c."createdAt" DESC
      LIMIT 1
    `;

    const consultationResult = await client.query(consultationQuery, [patientID]);

    if (consultationResult.rows.length === 0) {
      res
        .status(404)
        .send(new ApiResponse(httpStatus.NOT_FOUND, 'No consultations found for this patient', null, false, true));
      return;
    }

    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', consultationResult.rows[0], true));
  } catch (e) {
    logger.error('Error fetching last consultation by zohoId:', e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occurred while fetching the consultation');
  } finally {
    if (client) client.release();
  }
});

export const updateZohoLeadCancelBooking: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const { leadId } = body;
    const headers = await ZohoAuth.getHeaders();
    await axios.put(
      `${zohoLeadURL}/${leadId}`,
      {
        data: [
          {
            Member_Status: '20 - Booking Not Confirmed',
            Online_Booking_Confirmed: 'No',
          },
        ],
      },
      { headers },
    );
    res.status(200).send({});
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  }
});

export const updateZohoLeadBookingOnlineStatus: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const { leadId } = body;

    const headers = await ZohoAuth.getHeaders();
    await axios.put(
      `${zohoLeadURL}/${leadId}`,
      {
        data: [
          {
            Online_Booking_Confirmed: 'Yes',
          },
        ],
      },
      { headers },
    );
    res.status(200).send({});
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  }
});

export const updateZohoLeadReachConfirmationPage: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const { leadId } = body;

    const headers = await ZohoAuth.getHeaders();
    await axios.put(
      `${zohoLeadURL}/${leadId}`,
      {
        data: [
          {
            Booking_Confirmation_Page_Reached: 'Yes',
          },
        ],
      },
      { headers },
    );
    res.status(200).send({});
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  }
});

export const updateZohoLeadBookingStatus: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const { leadId } = body;
    const headers = await ZohoAuth.getHeaders();
    await axios.put(
      `${zohoLeadURL}/${leadId}`,
      {
        data: [
          {
            Booked_On_Call_Patient_Confirmation: 'Yes',
          },
        ],
      },
      { headers },
    );
    res.status(200).send();
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  }
});

export const updateZohoLeadMemberStatus: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const { leadId, memberStatus } = body;

    if (!leadId) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Missing leadId');
    }

    const headers = await ZohoAuth.getHeaders();
    await axios.put(
      `${zohoLeadURL}/${leadId}`,
      {
        data: [
          {
            Member_Status: memberStatus || '8 - Booking Page Reached',
          },
        ],
      },
      { headers },
    );
    res.status(200).send({
      success: true,
      message: 'Member status updated successfully',
    });
  } catch (e) {
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occurred while updating member status');
  }
});

export const postHarvestDischarge: RequestHandler = catchAll(async (req, res) => {
  let client;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    const body = req.body;
    const headers = await ZohoAuth.getHeaders();
    const { email, firstname, lastname, phone } = body;
    const password = Math.random().toString(36).slice(2, 15);
    const zohoLeadFound = await ZohoAuth.getZohoLeadByEmail(email);

    let leadId = '';
    if (zohoLeadFound == null) {
      const data = {
        data: [
          {
            Email: email,
            First_Name: firstname,
            Last_Name: lastname,
            Phone: phone,
            Mobile: phone,
            Member_Status: '18 - Discharge Challenge Quiz Completed',
          },
        ],
      };
      const result = await axios.post(`${zohoLeadURL}`, data, { headers });
      leadId = result.data.data?.[0].details?.id;
    } else {
      leadId = zohoLeadFound.data?.[0].id;
    }

    const wpUser = await WordPressUtils.getWPUser(email);
    let wpUserId: string;
    if (wpUser == null) {
      const userData = {
        username: email,
        first_name: firstname,
        last_name: lastname,
        email: email,
        password: password,
        meta: {
          billing_phone: body.phone.trim(),
          shipping_phone: body.phone.trim(),
          _discharge_quiz: body,
          _zoho_crm_lead_id: leadId,
        },
      };

      const resultWP = await WordPressUtils.createWPUser(userData);
      wpUserId = resultWP.id;
    } else {
      wpUserId = wpUser.id;
    }
    const dataZoho = {
      data: [
        {
          WP_User_ID: wpUserId,
        },
      ],
    };
    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
    const checkPatient = `SELECT * from patient WHERE email=$1`;
    const existingPatient = await client.query(checkPatient, [email]);
    if (!existingPatient.rows || existingPatient.rows.length < 1) {
      const query = `
    INSERT INTO patient
    ("fullName", email, password,mobile,"patientID","zohoID", "lastCompletedForm", "createdAt", "updatedAt")
    VALUES ($1, $2, $3, $4,$5,$6,$7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

      await client.query(query, [`${firstname} ${lastname}`, email, password, phone, leadId, leadId, 'registration']);
    }
    if (config.secureFunnelCookie) {
      res.cookie('lead_id', leadId, {
        httpOnly: true,
        sameSite: 'none',
        secure: true,
      });
    } else {
      res.cookie('lead_id', leadId, {
        httpOnly: true,
        secure: false,
      });
    }
    await client.query('COMMIT');
    res.status(200).send({});
  } catch (e) {
    await client.query('ROLLBACK');
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    client.release();
  }
});

export const postHarvestNoAlcohol: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');
    const body = req.body;
    const headers = await ZohoAuth.getHeaders();
    const { email, firstname, lastname, phone } = body;
    const password = Math.random().toString(36).slice(2, 15);
    const zohoLeadFound = await ZohoAuth.getZohoLeadByEmail(email);
    let leadId = '';
    if (zohoLeadFound == null) {
      const data = {
        data: [
          {
            Email: email,
            First_Name: firstname,
            Phone: phone,
            Mobile: phone,
            Last_Name: lastname,
            Member_Status: '19 - No Alcohol Challenge Quiz Completed',
          },
        ],
      };
      const result = await axios.post(`${zohoLeadURL}`, data, { headers });
      leadId = result.data.data?.[0].details?.id;
    } else {
      leadId = zohoLeadFound.data?.[0].id;
    }
    const wpUser = await WordPressUtils.getWPUser(email);
    let wpUserId: string;
    if (wpUser == null) {
      const userData = {
        username: email,
        first_name: firstname,
        last_name: lastname,
        email: email,
        password: password,
        meta: {
          _no_alcohol_challenge: body,
          billing_phone: body.phone.trim(),
          shipping_phone: body.phone.trim(),
          _zoho_crm_lead_id: leadId,
        },
      };
      const resultWP = await WordPressUtils.createWPUser(userData);
      wpUserId = resultWP.id;
    } else {
      wpUserId = wpUser.id;
    }
    const dataZoho = {
      data: [
        {
          WP_User_ID: wpUserId,
        },
      ],
    };
    await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
    const checkPatient = `SELECT * from patient WHERE email=$1`;
    const existingPatient = await client.query(checkPatient, [email]);
    if (!existingPatient.rows || existingPatient.rows.length < 1) {
      const query = `
    INSERT INTO patient
    ("fullName", email, password,mobile,"patientID","zohoID", "lastCompletedForm", "createdAt", "updatedAt")
    VALUES ($1, $2, $3, $4,$5,$6,$7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;

      await client.query(query, [`${firstname} ${lastname}`, email, password, phone, leadId, leadId, 'registration']);
    }
    if (config.secureFunnelCookie) {
      res.cookie('lead_id', leadId, {
        httpOnly: true,
        sameSite: 'none',
        secure: true,
      });
    } else {
      res.cookie('lead_id', leadId, {
        httpOnly: true,
        secure: false,
      });
    }
    await client.query('COMMIT');
    res.status(200).send({});
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    console.log(e);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occured');
  } finally {
    if (client) client.release();
  }
});

/**
 * Submit patient consent form
 * This endpoint handles the submission of the patient consent form and updates the patient record
 */
export const submitConsentForm: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');

    // Get consent form data from request body
    const consentData = req.body as ConsentFormData;

    // Determine if we're using a cookie or the data from the request body
    let patientId: string | undefined;
    let zohoId: string | undefined;
    let isContact = false;
    let dbPatientId: string | undefined;

    // Get IP address and user agent for audit purposes
    let ipAddress = '';
    let deviceDetails = '';

    if (consentData.signatureEvidence) {
      // Use the signatureEvidence from the request body
      ipAddress = consentData.signatureEvidence.ipAddress;

      // Handle deviceInfo based on its type
      if (typeof consentData.signatureEvidence.deviceInfo === 'string') {
        deviceDetails = consentData.signatureEvidence.deviceInfo;
      } else {
        // It's a DeviceInfo object, convert to string
        deviceDetails = JSON.stringify(consentData.signatureEvidence.deviceInfo);
      }
    } else if (consentData.ip_address && consentData.device_details) {
      // Fallback to individual fields if provided
      ipAddress = consentData.ip_address;
      deviceDetails = consentData.device_details;
    } else {
      // Fallback to request headers if not provided in the body
      ipAddress = req.ip || req.socket.remoteAddress || '';
      deviceDetails = req.headers['user-agent'] || '';
    }

    // Check if we have userDetails in the request body
    if (consentData.userDetails) {
      // Use the patient data from the request body
      patientId = consentData.userDetails.patientID;
      zohoId = consentData.userDetails.zohoID;

      // If we have a contactId in the request, use that instead
      if (consentData.contactId) {
        isContact = true;
        // Remove the 'p' prefix if it exists
        zohoId = consentData.contactId.startsWith('p') ? consentData.contactId.substring(1) : consentData.contactId;

        logger.info(`Using contact ID from request: ${zohoId}`);
      } else if (consentData.zohoID) {
        // If zohoID is provided directly in the request
        isContact = consentData.zohoID.startsWith('p');
        zohoId = isContact ? consentData.zohoID.substring(1) : consentData.zohoID;
      }

      // Get database patient ID (UUID) for the consent submission record in one query
      const patientResult = await client.query('SELECT id FROM patient WHERE "patientID" = $1', [patientId]);

      if (!patientResult.rows || patientResult.rows.length === 0) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Patient not found in database');
      }

      dbPatientId = patientResult.rows[0].id;
    } else {
      // Try to find the patient using various methods

      // First check if we have an email in the request
      if (consentData.userEmail) {
        // Get patient by email
        const patientResult = await client.query('SELECT id, "patientID", "zohoID" FROM patient WHERE email = $1', [
          consentData.userEmail.toLowerCase(),
        ]);

        if (patientResult.rows && patientResult.rows.length > 0) {
          patientId = patientResult.rows[0].patientID;
          zohoId = patientResult.rows[0].zohoID;
          dbPatientId = patientResult.rows[0].id;

          // Check if this is a contact ID (starts with 'p')
          if (consentData.contactId) {
            isContact = true;
            zohoId = consentData.contactId.startsWith('p') ? consentData.contactId.substring(1) : consentData.contactId;

            logger.info(`Using contact ID from request: ${zohoId}`);
          } else if (consentData.zohoID) {
            isContact = consentData.zohoID.startsWith('p');
            zohoId = isContact ? consentData.zohoID.substring(1) : consentData.zohoID;
          }
        }
      }

      // Check if we already found the patient by email
      if (!patientId || !dbPatientId) {
        // Fall back to cookie-based authentication
        const cookie = req.headers.cookie;
        const leadId = getCookie(cookie);

        if (!leadId) {
          // If we have a userId but no cookie, try to find the patient by userId
          if (consentData.userId) {
            // Get patient by userId (which might be an email)
            const patientResult = await client.query('SELECT id, "patientID", "zohoID" FROM patient WHERE email = $1', [
              consentData.userId.toLowerCase(),
            ]);

            if (patientResult.rows && patientResult.rows.length > 0) {
              patientId = patientResult.rows[0].patientID;
              zohoId = patientResult.rows[0].zohoID;
              dbPatientId = patientResult.rows[0].id;

              // Check if this is a contact ID
              if (consentData.contactId) {
                isContact = true;
                zohoId = consentData.contactId.startsWith('p')
                  ? consentData.contactId.substring(1)
                  : consentData.contactId;
              } else if (consentData.zohoID) {
                isContact = consentData.zohoID.startsWith('p');
                zohoId = isContact ? consentData.zohoID.substring(1) : consentData.zohoID;
              }
            }
          }

          // If we still don't have a patient, throw an error
          if (!patientId || !dbPatientId) {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              'Unable to identify patient. Please provide valid patient information.',
            );
          }
        } else {
          zohoId = leadId;

          // Get both patient ID and database ID in a single query
          const patientResult = await client.query('SELECT id, "patientID" FROM patient WHERE "zohoID" = $1', [leadId]);

          if (!patientResult.rows || patientResult.rows.length === 0) {
            throw new ApiError(httpStatus.NOT_FOUND, 'Patient not found');
          }

          patientId = patientResult.rows[0].patientID;
          dbPatientId = patientResult.rows[0].id;
        }
      }
    }

    // Final check to make sure we have all required data
    if (!patientId || !dbPatientId || !zohoId) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Unable to identify patient. Missing required patient information.');
    }

    // Insert consent submission record
    const insertConsentQuery = `
      INSERT INTO consentsubmission (
        patient_id,
        voluntary_consent,
        legally_competent,
        sufficient_information,
        understanding_risks,
        medical_cannabis_unapproved,
        illegal_prescription,
        drug_interactions,
        no_use_while_treated,
        illegal_to_minors,
        signature,
        ip_address,
        device_details
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING id
    `;

    const consentValues = [
      dbPatientId, // Using the database UUID we retrieved earlier
      consentData.voluntary_consent,
      consentData.legally_competent,
      consentData.sufficient_information,
      consentData.understanding_risks,
      consentData.medical_cannabis_unapproved,
      consentData.illegal_prescription,
      consentData.drug_interactions,
      consentData.no_use_while_treated,
      consentData.illegal_to_minors,
      consentData.signature,
      ipAddress,
      deviceDetails,
    ];

    const consentResult = await client.query(insertConsentQuery, consentValues);

    try {
      // Now update the patient record
      const updateResult = await client.query(
        'UPDATE patient SET "consent_form_completed" = TRUE, "lastCompletedForm" = $1, "updatedAt" = CURRENT_TIMESTAMP WHERE "patientID" = $2 RETURNING "consent_form_completed", "patientID", email',
        ['consent', patientId],
      );

      if (updateResult.rowCount === 0) {
        logger.warn(`No patient record was updated for patientID: ${patientId}`);

        // // Try updating by database ID as a fallback
        // const updateByIdResult = await client.query(
        //   'UPDATE patient SET "consent_form_completed" = TRUE, "lastCompletedForm" = $1, "updatedAt" = CURRENT_TIMESTAMP WHERE id = $2 RETURNING "consent_form_completed", "patientID", email',
        //   ['consent', dbPatientId]
        // );

        // if (updateByIdResult.rowCount === 0) {
        //   logger.error(`Failed to update patient by either patientID or database ID`);
        //   throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to update patient consent status');
        // }
      } else {
        //logger.info(`Patient record updated successfully by patientID. patientID: ${updateResult.rows[0].patientID}, email: ${updateResult.rows[0].email}, consent_form_completed: ${updateResult.rows[0].consent_form_completed}`);
      }
    } catch (updateError) {
      logger.error(`Error updating patient record: ${updateError}`);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to update patient consent status');
    }

    // Update Zoho CRM with consent form status
    const headers = await ZohoAuth.getHeaders();
    const dataZoho = {
      data: [
        {
          Consent_Form_Completed: 'Yes',
        },
      ],
    };

    // Determine whether to update a Lead or a Contact in Zoho
    if (isContact) {
      logger.info(`Updating Zoho Contact with ID: ${zohoId}`);
      await axios.put(`${zohoContactURL}/${zohoId}`, dataZoho, { headers });
    } else {
      logger.info(`Updating Zoho Lead with ID: ${zohoId}`);
      await axios.put(`${zohoLeadURL}/${zohoId}`, dataZoho, { headers });
    }

    await client.query('COMMIT');
    res.status(200).send({
      success: true,
      message: 'Consent form submitted successfully',
      data: { consentId: consentResult.rows[0].id },
    });
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    logger.error('Error submitting consent form:', e);
    if (e instanceof ApiError) {
      throw e;
    }
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occurred while submitting the consent form');
  } finally {
    if (client) client.release();
  }
});

/**
 * Get consent form status for a patient
 * This endpoint checks if a patient has completed the consent form
 */
export const getConsentFormStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();

    // Check if we have an email query parameter
    const email = req.query.email as string;

    if (email) {
      // Decode the email if it's URL encoded
      const decodedEmail = decodeURIComponent(email);

      // Clean up the email - remove any query parameters that might be attached
      const cleanEmail = decodedEmail.split('?')[0];

      logger.info(`Checking consent form status for email: ${cleanEmail}`);

      // Check if patient has completed consent form by email
      const result = await client.query(
        'SELECT "consent_form_completed", "zohoID", "patientID" FROM patient WHERE email = $1',
        [cleanEmail.toLowerCase()],
      );

      if (!result.rows || result.rows.length === 0) {
        res.status(httpStatus.NOT_FOUND).send({
          success: false,
          message: 'Patient not found',
          data: null,
        });
        return;
      }

      res.status(200).send({
        success: true,
        data: {
          consentFormCompleted: result.rows[0].consent_form_completed || false,
          zohoID: result.rows[0].zohoID,
          patientID: result.rows[0].patientID,
        },
      });
      return;
    }

    // Fall back to cookie-based authentication
    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    if (!leadId) {
      res.status(httpStatus.UNAUTHORIZED).send({
        success: false,
        message: 'User not authenticated',
        data: null,
      });
      return;
    }

    // Check if patient has completed consent form by zohoID
    const result = await client.query('SELECT "consent_form_completed", "patientID" FROM patient WHERE "zohoID" = $1', [
      leadId,
    ]);

    if (!result.rows || result.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        message: 'Patient not found',
        data: null,
      });
      return;
    }

    res.status(200).send({
      success: true,
      data: {
        consentFormCompleted: result.rows[0].consent_form_completed || false,
        patientID: result.rows[0].patientID,
      },
    });
  } catch (e) {
    logger.error('Error checking consent form status:', e);

    // Handle errors gracefully with a proper response instead of throwing
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      message: e instanceof ApiError ? e.message : 'An error occurred while checking consent form status',
      data: null,
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get patient details by email
 * This endpoint retrieves patient information using their email address
 */
export const getPatientByEmail: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();

    // Get email from query parameter
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        message: 'Email parameter is required',
        data: null,
      });
      return;
    }

    // Decode the email if it's URL encoded
    const decodedEmail = decodeURIComponent(email);

    // Clean up the email - remove any query parameters that might be attached
    // This handles cases like "<EMAIL>?contact" by removing "?contact"
    const cleanEmail = decodedEmail.split('?')[0];

    logger.info(`Searching for patient with email: ${cleanEmail}`);

    // Query patient by email
    const query = `
      SELECT
        id,
        "fullName",
        email,
        "mobile" as phone,
        "phoneVerified",
        "lastCompletedForm",
        "patientID",
        "zohoID",
        "returningPatient",
        "dob",
        state,
        "consent_form_completed"
      FROM patient
      WHERE email = $1
    `;

    const result = await client.query(query, [cleanEmail.toLowerCase()]);

    if (!result.rows || result.rows.length === 0) {
      // Instead of throwing an error, return a 404 with a structured response
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        message: 'Patient not found',
        data: null,
      });
      return;
    }

    // Return patient details
    res.status(200).send({
      success: true,
      data: result.rows[0],
    });
  } catch (e) {
    logger.error('Error retrieving patient by email:', e);

    // Handle errors gracefully with a proper response instead of throwing
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      message: e instanceof ApiError ? e.message : 'An error occurred while retrieving patient information',
      data: null,
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get invoice from Order ID
 */
export const getZohoInvoiceByOrderId: RequestHandler = catchAll(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const result = await ZohoAuth.getInvoiceByOrderId(orderId);
    if (result != null && result.invoices && result.invoices.length > 0) {
      const invoice = result.invoices[0];

      const invoice_url = invoice.invoice_url.replace('/secure?', '/securepay?');

      const response = {
        Payment_Link_URL: invoice_url,
        currency_symbol: invoice.currency_symbol,
        Order_Total: invoice.total,
      };

      res.status(200).send(response);
    } else {
      res.status(400).send('No invoice found');
    }
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

/**
 * Get Payment Link from Order ID
 */
export const getZohoPaymentLinkByOrderId: RequestHandler = catchAll(async (req, res) => {
  try {
    const orderId = req.params.orderId;
    const result = await ZohoAuth.getPaymentLinkByOrderId(orderId);
    if (result != null && result.payment_link) {
      const payment_link = result.payment_link.url;
      const total = result.payment_link.payment_amount;

      const response = {
        Payment_Link_URL: payment_link,
        Order_Total: total,
      };

      res.status(200).send(response);
    } else {
      res.status(400).send('No invoice found');
    }
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

/**
 * Set Invoice as paid
 */
export const setZohoInvoicePaid: RequestHandler = catchAll(async (req, res) => {
  try {
    const paymentNumber = req.params.paymentNumber;
    const result = await ZohoAuth.setZohoInvoicePaid(paymentNumber);
    if (result != null) {
      const response = {
        success: true,
      };

      res.status(200).send(response);
    } else {
      res.status(400).send('No payment found');
    }
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

export const setZohoInvoicePaymentStatus: RequestHandler = catchAll(async (req, res) => {
  try {
    const body = req.body;
    const payment_status = body.type;
    const payment_description = body.data.object.description;
    const name = body.data.object.metadata.Name;
    const email = body.data.object.metadata.email;
    const amount =
      body.data.object.amount_received > 0 ? body.data.object.amount_received / 100 : body.data.object.amount_received; // stripe amount in cents
    const message = body.data.object.last_payment_error?.message ?? '';

    const result = await ZohoAuth.setZohoInvoicePaymentStatusFromStripe(
      payment_status,
      payment_description,
      name,
      email,
      amount,
      message,
    );
    if (result != null) {
      if (result.error && result.error == 'not_marked_paid') {
        res.status(500).send('Payment not updated in Zoho yet.');
      } else {
        res.status(200).send(result);
      }
    } else {
      res.status(400).send('No payment found');
    }
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

export const getStockUpdates: RequestHandler = catchAll(async (_req, res) => {
  try {
    const result = await WordPressUtils.getStockUpdates();
    if (result != null) {
      res.status(200).send(result);
    } else {
      res.status(400).send('No stock found');
    }
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

export const storeUTMParamsOnLead: RequestHandler = catchAll(async (req, res) => {
  try {
    const leadId = req.params.LeadID;

    const utm_source = req.body.utm_source;
    const utm_medium = req.body.utm_medium;
    const utm_campaign = req.body.utm_campaign;
    // const utm_term = req.body.utm_term;
    const utm_content = req.body.utm_content;
    const fbclid = req.body.fbclid;

    const headers = await ZohoAuth.getHeaders();
    const data = {
      data: [
        {
          utm_source: utm_source,
          utm_medium: utm_medium,
          utm_campaign: utm_campaign,
          utm_content: utm_content,
          fbclid: fbclid,
        },
      ],
    };
    axios.put(`${zohoLeadURL}/${leadId}`, data, { headers });

    res.status(200).send('Data submitted');
  } catch (e) {
    console.log(e);
    res.status(400).send('An error occured');
  }
});

/**
 * Submit THC increase questionnaire
 */
export const submitTHCIncreaseQuestionnaire: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');

    const { questionsAndAnswers, totalScore, maxScore, isEligible } = req.body;
    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    if (!leadId) {
      res.status(httpStatus.UNAUTHORIZED).send({
        success: false,
        error: 'Authentication required',
        code: 401
      });
      return;
    }

    // Get patient details
    const patientQuery = `SELECT id, "patientID", email, "zohoID" FROM patient WHERE "zohoID" = $1`;
    const patientResult = await client.query(patientQuery, [leadId]);

    if (!patientResult.rows || patientResult.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        error: 'Patient not found',
        code: 404
      });
      return;
    }

    const patient = patientResult.rows[0];
    const questionnaireId = uuid();
    const ipAddress = req.ip || req.socket.remoteAddress || '';
    const userAgent = req.headers['user-agent'] || '';

    // Insert questionnaire
    const insertQuery = `
      INSERT INTO thc_increase_questionnaire (
        id, patient_id, email, zoho_id, questionnaire_data,
        total_score, max_score, is_eligible, status,
        ip_address, user_agent, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
      RETURNING id
    `;

    await client.query(insertQuery, [
      questionnaireId,
      patient.patientID,
      patient.email,
      patient.zohoID,
      JSON.stringify(questionsAndAnswers),
      totalScore,
      maxScore,
      isEligible,
      'submitted',
      ipAddress,
      userAgent
    ]);

    await client.query('COMMIT');

    // Update Zoho contact with patient request information (non-blocking)
    try {
      const zohoUpdateSuccess = await updateContactPatientRequest(patient.email, 'thc_increase', isEligible);
      if (zohoUpdateSuccess) {
        logger.info(`Successfully updated Zoho contact with patient request info for: ${patient.email}`);
      } else {
        logger.warn(`Failed to update Zoho contact with patient request info for: ${patient.email}`);
      }
    } catch (zohoError) {
      // Don't fail the request if Zoho update fails
      logger.error('Failed to update Zoho contact with patient request info for THC increase:', zohoError);
    }

    // Send Slack notification for new request submission
    try {
      await requestModerationService.sendRequestSubmissionNotification({
        id: questionnaireId,
        type: 'thc_increase',
        patient_id: patient.patientID,
        patient_name: patient.fullName,
        email: patient.email,
        total_score: totalScore,
        max_score: maxScore,
        is_eligible: isEligible,
        status: 'submitted',
        created_at: new Date().toISOString()
      });
    } catch (slackError) {
      // Don't fail the request if Slack notification fails
      logger.error('Failed to send Slack notification for THC increase request:', slackError);
    }

    res.status(200).send({
      success: true,
      message: 'THC increase questionnaire submitted successfully',
      data: {
        id: questionnaireId,
        totalScore,
        isEligible,
        status: 'submitted'
      }
    });
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    logger.error('Error submitting THC questionnaire:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'Invalid questionnaire data',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get THC questionnaire status
 */
export const getTHCQuestionnaireStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        error: 'Email parameter is required',
        code: 400
      });
      return;
    }

    const query = `
      SELECT
        id, total_score, max_score, is_eligible, status,
        created_at as submitted_at, reviewed_at, approved_at
      FROM thc_increase_questionnaire
      WHERE email = $1
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query, [email.toLowerCase()]);

    if (!result.rows || result.rows.length === 0) {
      res.status(200).send({
        success: false,
        message: 'No THC increase questionnaire found for this patient'
      });
      return;
    }

    const questionnaire = result.rows[0];
    res.status(200).send({
      success: true,
      questionnaire: {
        id: questionnaire.id,
        totalScore: questionnaire.total_score,
        maxScore: questionnaire.max_score,
        isEligible: questionnaire.is_eligible,
        status: questionnaire.status,
        submittedAt: questionnaire.submitted_at,
        reviewedAt: questionnaire.reviewed_at,
        approvedAt: questionnaire.approved_at
      }
    });
  } catch (e) {
    logger.error('Error getting THC questionnaire status:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'An error occurred while retrieving questionnaire status',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Submit ExtendTP questionnaire
 */
export const submitExtendTPQuestionnaire: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');

    const { questionsAndAnswers, totalScore, maxScore, isEligible } = req.body;
    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    if (!leadId) {
      res.status(httpStatus.UNAUTHORIZED).send({
        success: false,
        error: 'Authentication required',
        code: 401
      });
      return;
    }

    // Get patient details
    const patientQuery = `SELECT id, "patientID", email, "zohoID" FROM patient WHERE "zohoID" = $1`;
    const patientResult = await client.query(patientQuery, [leadId]);

    if (!patientResult.rows || patientResult.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        error: 'Patient not found',
        code: 404
      });
      return;
    }

    const patient = patientResult.rows[0];
    const questionnaireId = uuid();
    const ipAddress = req.ip || req.socket.remoteAddress || '';
    const userAgent = req.headers['user-agent'] || '';


    // Insert questionnaire
    const insertQuery = `
      INSERT INTO extend_tp_questionnaire (
        id, patient_id, email, zoho_id, questionnaire_data,
        total_score, max_score, is_eligible, status,
        ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id
    `;

    await client.query(insertQuery, [
      questionnaireId,
      patient.patientID,
      patient.email,
      patient.zohoID,
      JSON.stringify(questionsAndAnswers),
      totalScore,
      maxScore,
      isEligible,
      'submitted',
      ipAddress,
      userAgent
    ]);

    await client.query('COMMIT');

    // Update Zoho contact with patient request information (non-blocking)
    try {
      const zohoUpdateSuccess = await updateContactPatientRequest(patient.email, 'extend_tp', isEligible);
      if (zohoUpdateSuccess) {
        logger.info(`Successfully updated Zoho contact with patient request info for: ${patient.email}`);
      } else {
        logger.warn(`Failed to update Zoho contact with patient request info for: ${patient.email}`);
      }
    } catch (zohoError) {
      // Don't fail the request if Zoho update fails
      logger.error('Failed to update Zoho contact with patient request info for ExtendTP:', zohoError);
    }

    // Send Slack notification for new request submission
    try {
      await requestModerationService.sendRequestSubmissionNotification({
        id: questionnaireId,
        type: 'extend_tp',
        patient_id: patient.patientID,
        patient_name: patient.fullName,
        email: patient.email,
        total_score: totalScore,
        max_score: maxScore,
        is_eligible: isEligible,
        status: 'submitted',
        created_at: new Date().toISOString()
      });
    } catch (slackError) {
      // Don't fail the request if Slack notification fails
      logger.error('Failed to send Slack notification for ExtendTP request:', slackError);
    }

    res.status(200).send({
      success: true,
      message: 'ExtendTP questionnaire submitted successfully',
      data: {
        id: questionnaireId,
        totalScore,
        isEligible,
        status: 'submitted'
      }
    });
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    logger.error('Error submitting ExtendTP questionnaire:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'Invalid questionnaire data',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get ExtendTP questionnaire status
 */
export const getExtendTPQuestionnaireStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        error: 'Email parameter is required',
        code: 400
      });
      return;
    }

    const query = `
      SELECT
        id, total_score, max_score, is_eligible, status,
        created_at as submitted_at, reviewed_at, approved_at
      FROM extend_tp_questionnaire
      WHERE email = $1
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query, [email.toLowerCase()]);

    if (!result.rows || result.rows.length === 0) {
      res.status(200).send({
        success: false,
        message: 'No ExtendTP questionnaire found for this patient'
      });
      return;
    }

    const questionnaire = result.rows[0];
    res.status(200).send({
      success: true,
      questionnaire: {
        id: questionnaire.id,
        totalScore: questionnaire.total_score,
        maxScore: questionnaire.max_score,
        isEligible: questionnaire.is_eligible,
        status: questionnaire.status,
        submittedAt: questionnaire.submitted_at,
        reviewedAt: questionnaire.reviewed_at,
        approvedAt: questionnaire.approved_at
      }
    });
  } catch (e) {
    logger.error('Error getting ExtendTP questionnaire status:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'An error occurred while retrieving questionnaire status',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Submit Add 22% THC questionnaire
 */
export const submitAdd22THCQuestionnaire: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');

    const { questionsAndAnswers, totalScore, maxScore, isEligible } = req.body;
    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    if (!leadId) {
      res.status(httpStatus.UNAUTHORIZED).send({
        success: false,
        error: 'Authentication required',
        code: 401
      });
      return;
    }

    // Get patient details
    const patientQuery = `SELECT id, "patientID", email, "zohoID", "fullName" FROM patient WHERE "zohoID" = $1`;
    const patientResult = await client.query(patientQuery, [leadId]);

    if (!patientResult.rows || patientResult.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        error: 'Patient not found',
        code: 404
      });
      return;
    }

    const patient = patientResult.rows[0];

   

    // Note: We allow 22% THC addition requests regardless of current plan composition
    // This accommodates patients with older treatment plans or plans created outside our system

    const questionnaireId = uuid();
    // const ipAddress = req.ip || req.socket.remoteAddress || '';
    // const userAgent = req.headers['user-agent'] || '';

    // Insert questionnaire
    const insertQuery = `
      INSERT INTO add_22_thc_questionnaire (
        id, patient_id, email, zoho_id, questionnaire_data,
        total_score, max_score, is_eligible, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id
    `;

    await client.query(insertQuery, [
      questionnaireId,
      patient.patientID,
      patient.email,
      patient.zohoID,
      JSON.stringify(questionsAndAnswers),
      totalScore,
      maxScore,
      isEligible,
      'submitted'
    ]);

    await client.query('COMMIT');

    // Update Zoho contact with patient request information (non-blocking)
    try {
      const zohoUpdateSuccess = await updateContactPatientRequest(patient.email, 'add_22_thc', isEligible);
      if (zohoUpdateSuccess) {
        logger.info(`Successfully updated Zoho contact with patient request info for: ${patient.email}`);
      } else {
        logger.warn(`Failed to update Zoho contact with patient request info for: ${patient.email}`);
      }
    } catch (zohoError) {
      // Don't fail the request if Zoho update fails
      logger.error('Failed to update Zoho contact with patient request info for Add 22% THC:', zohoError);
    }

    // Send Slack notification for new request submission
    try {
      await requestModerationService.sendRequestSubmissionNotification({
        id: questionnaireId,
        type: 'add_22_thc',
        patient_id: patient.patientID,
        patient_name: patient.fullName,
        email: patient.email,
        total_score: totalScore,
        max_score: maxScore,
        is_eligible: isEligible,
        status: 'submitted',
        created_at: new Date().toISOString()
      });
    } catch (slackError) {
      // Don't fail the request if Slack notification fails
      logger.error('Failed to send Slack notification for Add 22% THC request:', slackError);
    }

    res.status(200).send({
      success: true,
      message: '22% THC addition questionnaire submitted successfully',
      data: {
        id: questionnaireId,
        totalScore,
        isEligible,
        status: 'submitted'
      }
    });
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    logger.error('Error submitting Add 22% THC questionnaire:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'Invalid questionnaire data',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get Add 22% THC questionnaire status
 */
export const getAdd22THCQuestionnaireStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        error: 'Email parameter is required',
        code: 400
      });
      return;
    }

    const query = `
      SELECT
        id, total_score, max_score, is_eligible, status,
        created_at as submitted_at, reviewed_at, approved_at
      FROM add_22_thc_questionnaire
      WHERE email = $1
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query, [email.toLowerCase()]);

    if (!result.rows || result.rows.length === 0) {
      res.status(200).send({
        success: false,
        message: 'No 22% THC addition questionnaire found for this patient'
      });
      return;
    }

    const questionnaire = result.rows[0];
    res.status(200).send({
      success: true,
      questionnaire: {
        id: questionnaire.id,
        totalScore: questionnaire.total_score,
        maxScore: questionnaire.max_score,
        isEligible: questionnaire.is_eligible,
        status: questionnaire.status,
        submittedAt: questionnaire.submitted_at,
        reviewedAt: questionnaire.reviewed_at,
        approvedAt: questionnaire.approved_at
      }
    });
  } catch (e) {
    logger.error('Error getting Add 22% THC questionnaire status:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'An error occurred while retrieving questionnaire status',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Submit Quantity Increase questionnaire
 */
export const submitQuantityIncreaseQuestionnaire: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    await client.query('BEGIN');

    const {
      questionsAndAnswers,
      selectedStrengths,
      strengthRequests,
      totalScore,
      maxScore,
      isEligible
    } = req.body;

    const cookie = req.headers.cookie;
    const leadId = getCookie(cookie);

    if (!leadId) {
      res.status(httpStatus.UNAUTHORIZED).send({
        success: false,
        error: 'Authentication required',
        code: 401
      });
      return;
    }

    // Get patient details
    const patientQuery = `SELECT id, "patientID", email, "zohoID", "fullName" FROM patient WHERE "zohoID" = $1`;
    const patientResult = await client.query(patientQuery, [leadId]);

    if (!patientResult.rows || patientResult.rows.length === 0) {
      res.status(httpStatus.NOT_FOUND).send({
        success: false,
        error: 'Patient not found',
        code: 404
      });
      return;
    }

    const patient = patientResult.rows[0];

    // Validate that patient has current treatment plan with requested strengths
    const treatmentPlanQuery = `
      SELECT
        tp."patientID",
        tp."totalQuantity22",
        tp."totalQuantity29",
        tp."createdAt"
      FROM TreatmentPlan tp
      WHERE tp."patientID" = $1 OR tp.email = $2
      ORDER BY tp."createdAt" DESC
      LIMIT 1
    `;

    const treatmentPlanResult = await client.query(treatmentPlanQuery, [patient.patientID, patient.email]);

    if (!treatmentPlanResult.rows || treatmentPlanResult.rows.length === 0) {
      res.status(httpStatus.FORBIDDEN).send({
        success: false,
        error: 'Patient not eligible for quantity increase - no active treatment plan found',
        code: 403
      });
      return;
    }

    // Note: We allow quantity increase requests for any strength, even if not present in current plan
    // This accommodates patients with older treatment plans or plans created outside our system

    const questionnaireId = uuid();

    // Insert questionnaire
    const insertQuery = `
      INSERT INTO quantity_increase_questionnaire (
        id, patient_id, email, zoho_id, questionnaire_data,
        selected_strengths, strength_requests, total_score, max_score, is_eligible, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING id
    `;

    await client.query(insertQuery, [
      questionnaireId,
      patient.patientID,
      patient.email,
      patient.zohoID,
      JSON.stringify(questionsAndAnswers),
      selectedStrengths,
      JSON.stringify(strengthRequests),
      totalScore,
      maxScore,
      isEligible,
      'submitted'
    ]);

    await client.query('COMMIT');

    // Update Zoho contact with patient request information (non-blocking)
    try {
      const zohoUpdateSuccess = await updateContactPatientRequest(patient.email, 'quantity_increase', isEligible);
      if (zohoUpdateSuccess) {
        logger.info(`Successfully updated Zoho contact with patient request info for: ${patient.email}`);
      } else {
        logger.warn(`Failed to update Zoho contact with patient request info for: ${patient.email}`);
      }
    } catch (zohoError) {
      // Don't fail the request if Zoho update fails
      logger.error('Failed to update Zoho contact with patient request info for Quantity Increase:', zohoError);
    }

    // Send Slack notification for new request submission
    try {
      await requestModerationService.sendRequestSubmissionNotification({
        id: questionnaireId,
        type: 'quantity_increase',
        patient_id: patient.patientID,
        patient_name: patient.fullName,
        email: patient.email,
        total_score: totalScore,
        max_score: maxScore,
        is_eligible: isEligible,
        status: 'submitted',
        created_at: new Date().toISOString()
      });
    } catch (slackError) {
      // Don't fail the request if Slack notification fails
      logger.error('Failed to send Slack notification for Quantity Increase request:', slackError);
    }

    res.status(200).send({
      success: true,
      message: 'Quantity increase questionnaire submitted successfully',
      data: {
        questionnaireId,
        totalScore,
        isEligible,
        status: 'submitted',
        strengthRequests
      }
    });
  } catch (e) {
    if (client) await client.query('ROLLBACK');
    logger.error('Error submitting Quantity Increase questionnaire:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'Invalid questionnaire data',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get Quantity Increase questionnaire status
 */
export const getQuantityIncreaseQuestionnaireStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        error: 'Email parameter is required',
        code: 400
      });
      return;
    }

    const query = `
      SELECT
        id, selected_strengths, strength_requests, total_score, max_score, is_eligible, status,
        created_at as submitted_at, reviewed_at, approved_at
      FROM quantity_increase_questionnaire
      WHERE email = $1
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query, [email.toLowerCase()]);

    if (!result.rows || result.rows.length === 0) {
      res.status(200).send({
        success: true,
        questionnaire: null
      });
      return;
    }

    const questionnaire = result.rows[0];
    res.status(200).send({
      success: true,
      questionnaire: {
        id: questionnaire.id,
        selectedStrengths: questionnaire.selected_strengths,
        strengthRequests: questionnaire.strength_requests,
        totalScore: questionnaire.total_score,
        maxScore: questionnaire.max_score,
        isEligible: questionnaire.is_eligible,
        status: questionnaire.status,
        submittedAt: questionnaire.submitted_at,
        reviewedAt: questionnaire.reviewed_at,
        approvedAt: questionnaire.approved_at
      }
    });
  } catch (e) {
    logger.error('Error getting Quantity Increase questionnaire status:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'An error occurred while retrieving questionnaire status',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});

/**
 * Get Patient Quantity Status (Helper Endpoint)
 */
export const getPatientQuantityStatus: RequestHandler = catchAll(async (req, res) => {
  let client: import('pg').PoolClient | undefined;
  try {
    client = await db.connect();
    const email = req.query.email as string;

    if (!email) {
      res.status(httpStatus.BAD_REQUEST).send({
        success: false,
        error: 'Email parameter is required',
        code: 400
      });
      return;
    }

    // Get patient's current treatment plan
    const treatmentPlanQuery = `
      SELECT
        tp."totalQuantity22",
        tp."totalQuantity29",
        tp."strengthAndConcentration22",
        tp."strengthAndConcentration29"
      FROM TreatmentPlan tp
      LEFT JOIN Patient p ON tp."patientID" = p."patientID" OR tp.email = p.email
      WHERE p.email = $1
      ORDER BY tp."createdAt" DESC
      LIMIT 1
    `;

    const treatmentPlanResult = await client.query(treatmentPlanQuery, [email.toLowerCase()]);

    if (!treatmentPlanResult.rows || treatmentPlanResult.rows.length === 0) {
      res.status(200).send({
        success: true,
        quantityStatus: {
          thc22: { current: 0, canIncrease: false, nextLevel: 0, maxLevel: 84 },
          thc29: { current: 0, canIncrease: false, nextLevel: 0, maxLevel: 84 },
          hasAnyIncreaseOptions: false
        },
        availableOptions: []
      });
      return;
    }

    const currentPlan = treatmentPlanResult.rows[0];

    // Define quantity progression levels
    const quantityLevels = [14, 28, 42, 56, 70, 84];

    const getNextLevel = (current: number): number => {
      const currentIndex = quantityLevels.indexOf(current);
      return currentIndex >= 0 && currentIndex < quantityLevels.length - 1
        ? quantityLevels[currentIndex + 1]
        : 0;
    };

    const canIncrease = (current: number): boolean => {
      return current > 0 && current < 84 && quantityLevels.includes(current);
    };

    const current22 = parseInt(currentPlan.totalQuantity22) || 0;
    const current29 = parseInt(currentPlan.totalQuantity29) || 0;

    const thc22Status = {
      current: current22,
      canIncrease: canIncrease(current22),
      nextLevel: getNextLevel(current22),
      maxLevel: 84
    };

    const thc29Status = {
      current: current29,
      canIncrease: canIncrease(current29),
      nextLevel: getNextLevel(current29),
      maxLevel: 84
    };

    const availableOptions: Array<{
      value: string;
      label: string;
      current: number;
      next: number;
    }> = [];

    if (thc22Status.canIncrease) {
      availableOptions.push({
        value: '22',
        label: `22% THC (${current22}g → ${thc22Status.nextLevel}g)`,
        current: current22,
        next: thc22Status.nextLevel
      });
    }

    if (thc29Status.canIncrease) {
      availableOptions.push({
        value: '29',
        label: `29% THC (${current29}g → ${thc29Status.nextLevel}g)`,
        current: current29,
        next: thc29Status.nextLevel
      });
    }

    res.status(200).send({
      success: true,
      quantityStatus: {
        thc22: thc22Status,
        thc29: thc29Status,
        hasAnyIncreaseOptions: availableOptions.length > 0
      },
      availableOptions
    });
  } catch (e) {
    logger.error('Error getting patient quantity status:', e);
    res.status(httpStatus.BAD_REQUEST).send({
      success: false,
      error: 'An error occurred while retrieving quantity status',
      code: 400
    });
  } finally {
    if (client) client.release();
  }
});
