import { WebClient, Block, KnownBlock } from '@slack/web-api';
import config from '../config';
import { db } from '../utils/db';
import { DateTime } from 'luxon';
import { logger } from '../config/logger';

interface DayAvailabilityData {
  totalSlots: number;
  remainingSlots: number;
  bookedSlots: number;
  noShowSlots: number;
  noShowRemaining: number;
  noShowBooked: number;
}

interface DayReport {
  date: string;
  dayName: string;
  availability: DayAvailabilityData;
}

interface WeeklyAvailabilityReport {
  weekStart: string;
  weekEnd: string;
  weekNumber: number;
  days: DayReport[];
  weekTotal: DayAvailabilityData;
}

class AvailabilityReportingService {
  private slack: WebClient;

  constructor() {
    this.slack = new WebClient(config.slackToken);
  }

  /**
   * Generate and send weekly availability report for the next 4 weeks
   */
  async sendWeeklyAvailabilityReport(): Promise<void> {
    try {

      const reportData = await this.generateFourWeekReport();
      const slackBlocks = this.formatSlackBlocks(reportData);

      await this.sendToSlack(slackBlocks);
      
      logger.info('Weekly availability report sent');
    } catch (error) {
      logger.error('Failed to send weekly availability report:', error);
      throw error;
    }
  }

  /**
   * Generate availability data for the next 4 weeks
   */
  async generateFourWeekReport(): Promise<WeeklyAvailabilityReport[]> {
    const reports: WeeklyAvailabilityReport[] = [];
    const now = DateTime.now().setZone('Australia/Sydney');

    // Find the current or next Monday (never past weeks)
    let startMonday = now.startOf('week'); // Current Monday

    // If we're past Monday (Tuesday-Sunday), move to next Monday
    if (now.weekday > 1) {
      startMonday = now.startOf('week').plus({ weeks: 1 });
    }

    // Ensure we never include past weeks by checking if startMonday is in the past
    if (startMonday < now.startOf('day')) {
      startMonday = now.startOf('week').plus({ weeks: 1 });
    }

    for (let week = 0; week < 4; week++) {
      const weekStart = startMonday.plus({ weeks: week });
      const weekEnd = weekStart.plus({ days: 4 }); // Friday instead of Sunday

      // Double-check: skip any week that starts before today
      if (weekStart >= now.startOf('day')) {
        const weekReport = await this.getAvailabilityForWeek(weekStart, weekEnd);
        weekReport.weekNumber = week + 1; // Set the week number

        reports.push(weekReport);
      }
    }

    return reports;
  }

  /**
   * Get availability data for a specific week with per-day breakdown
   */
  private async getAvailabilityForWeek(weekStart: DateTime, weekEnd: DateTime): Promise<WeeklyAvailabilityReport> {
    const client = await db.connect();

    try {
      const startDate = weekStart.toFormat('yyyy-MM-dd');
      const endDate = weekEnd.toFormat('yyyy-MM-dd');

      // Query for daily availability data with booking counts
      const dailyQuery = `
        SELECT
          r.date,
          r.day,
          COALESCE(SUM(r.availability), 0) as total_slots,
          COALESCE(SUM(r."noShowAvailability"), 0) as no_show_slots,
          COALESCE(SUM(r.availability - s.remaining), 0) as standard_booked,
          COALESCE(SUM(r."noShowAvailability" - s."noShowRemaining"), 0) as no_show_booked
        FROM range r
        LEFT JOIN slot s ON r.id = s.range_id
        WHERE r.date >= $1 AND r.date <= $2
        AND r.status = 'active'
        GROUP BY r.date, r.day
        ORDER BY r.date,
          CASE r.day
            WHEN 'Monday' THEN 1
            WHEN 'Tuesday' THEN 2
            WHEN 'Wednesday' THEN 3
            WHEN 'Thursday' THEN 4
            WHEN 'Friday' THEN 5
            WHEN 'Saturday' THEN 6
            WHEN 'Sunday' THEN 7
          END
      `;

      const dailyResult = await client.query(dailyQuery, [startDate, endDate]);

      // Build daily reports - ensure we have Monday through Friday for each week
      const dataMap = new Map<string, {
        date: string;
        day: string;
        total_slots: string;
        no_show_slots: string;
        standard_booked: string;
        no_show_booked: string;
      }>();
      dailyResult.rows.forEach(row => {
        dataMap.set(row.date, row);
      });

      const days: DayReport[] = [];
      const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

      // Generate all weekdays in the range
      let currentDate = weekStart;
      while (currentDate <= weekEnd) {
        const dayName = currentDate.toFormat('cccc'); // Full day name

        // Only include weekdays (Monday-Friday)
        if (dayNames.includes(dayName)) {
          const dateStr = currentDate.toFormat('yyyy-MM-dd');
          const row = dataMap.get(dateStr);

          if (row) {
            // We have data for this day
            const totalSlots = parseInt(row.total_slots, 10);
            const noShowSlots = parseInt(row.no_show_slots, 10);
            const standardBooked = parseInt(row.standard_booked, 10);
            const noShowBooked = parseInt(row.no_show_booked, 10);

            days.push({
              date: dateStr,
              dayName: dayName,
              availability: {
                totalSlots,
                remainingSlots: totalSlots - standardBooked,
                bookedSlots: standardBooked,
                noShowSlots,
                noShowRemaining: noShowSlots - noShowBooked,
                noShowBooked
              }
            });
          } else {
            // No data for this day - show as 0/0
            days.push({
              date: dateStr,
              dayName: dayName,
              availability: {
                totalSlots: 0,
                remainingSlots: 0,
                bookedSlots: 0,
                noShowSlots: 0,
                noShowRemaining: 0,
                noShowBooked: 0
              }
            });
          }
        }

        currentDate = currentDate.plus({ days: 1 });
      }

      // Calculate week totals
      const weekTotal: DayAvailabilityData = days.reduce(
        (total, day) => ({
          totalSlots: total.totalSlots + day.availability.totalSlots,
          remainingSlots: total.remainingSlots + day.availability.remainingSlots,
          bookedSlots: total.bookedSlots + day.availability.bookedSlots,
          noShowSlots: total.noShowSlots + day.availability.noShowSlots,
          noShowRemaining: total.noShowRemaining + day.availability.noShowRemaining,
          noShowBooked: total.noShowBooked + day.availability.noShowBooked
        }),
        { totalSlots: 0, remainingSlots: 0, bookedSlots: 0, noShowSlots: 0, noShowRemaining: 0, noShowBooked: 0 }
      );

      return {
        weekStart: weekStart.toFormat('yyyy-MM-dd'),
        weekEnd: weekEnd.toFormat('yyyy-MM-dd'),
        weekNumber: 0, // This will be set by the caller
        days,
        weekTotal
      };
    } finally {
      client.release();
    }
  }

  /**
   * Format the report data into professional Slack blocks
   */
  private formatSlackBlocks(reports: WeeklyAvailabilityReport[]): (Block | KnownBlock)[] {
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const blocks: (Block | KnownBlock)[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📊 Weekly Availability Summary',
          emoji: true,
        },
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Generated: ${timestamp} | Period: Next 4 weeks`,
          },
        ],
      },
      { type: 'divider' },
    ];

    reports.forEach((report, index) => {
      const weekStartFormatted = DateTime.fromISO(report.weekStart).toFormat('dd MMM');
      const weekEndFormatted = DateTime.fromISO(report.weekEnd).toFormat('dd MMM yyyy');

      // Week header
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Week ${report.weekNumber}: ${weekStartFormatted} - ${weekEndFormatted}*\n*Total Slots:* ${report.weekTotal.totalSlots}`,
        },
      });

      // Daily breakdown - table format like the image
      let tableText = `\`\`\`\n`;
      tableText += `Day          Date      Lew leads     No-Show\n`;
      tableText += `─────────────────────────────────────────────\n`;

      report.days.forEach(day => {
        const dayDate = DateTime.fromISO(day.date).toFormat('MMM dd');
        const standardUtilization = day.availability.totalSlots > 0
          ? ((day.availability.bookedSlots / day.availability.totalSlots) * 100).toFixed(0)
          : '0';
        const noShowUtilization = day.availability.noShowSlots > 0
          ? ((day.availability.noShowBooked / day.availability.noShowSlots) * 100).toFixed(0)
          : '0';

        const standardText = `${day.availability.bookedSlots}/${day.availability.totalSlots} (${standardUtilization}%)`;
        const noShowText = `${day.availability.noShowBooked}/${day.availability.noShowSlots} (${noShowUtilization}%)`;

        // Format with proper spacing for table alignment
        const dayName = day.dayName.padEnd(12);
        const dateFormatted = dayDate.padEnd(9);
        const standardFormatted = standardText.padEnd(13);

        tableText += `${dayName} ${dateFormatted} ${standardFormatted} ${noShowText}\n`;
      });

      tableText += `\`\`\`\n`;

      // Add daily breakdown table
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: tableText,
        },
      });

      // Week summary in clean format
      const weekStandardUtilization = report.weekTotal.totalSlots > 0
        ? ((report.weekTotal.bookedSlots / report.weekTotal.totalSlots) * 100).toFixed(0)
        : '0';
      const weekNoShowUtilization = report.weekTotal.noShowSlots > 0
        ? ((report.weekTotal.noShowBooked / report.weekTotal.noShowSlots) * 100).toFixed(0)
        : '0';

      const weekSummaryText = `*Week Summary:*\n` +
        `• Lew leads: ${report.weekTotal.bookedSlots}/${report.weekTotal.totalSlots} (${weekStandardUtilization}%)\n` +
        `• No-Show: ${report.weekTotal.noShowBooked}/${report.weekTotal.noShowSlots} (${weekNoShowUtilization}%)`;

      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: weekSummaryText,
        },
      });

      if (index < reports.length - 1) {
        blocks.push({ type: 'divider' });
      }
    });

    // 4-week summary
    const totalStandardSlots = reports.reduce((sum, r) => sum + r.weekTotal.totalSlots, 0);
    const totalStandardBooked = reports.reduce((sum, r) => sum + r.weekTotal.bookedSlots, 0);
    const totalNoShowSlots = reports.reduce((sum, r) => sum + r.weekTotal.noShowSlots, 0);
    const totalNoShowBooked = reports.reduce((sum, r) => sum + r.weekTotal.noShowBooked, 0);

    const overallStandardUtilization = totalStandardSlots > 0 ? ((totalStandardBooked / totalStandardSlots) * 100).toFixed(0) : '0';

    blocks.push({ type: 'divider' });

    // 4-Week Summary in table format
    const overallNoShowUtilization = totalNoShowSlots > 0
      ? ((totalNoShowBooked / totalNoShowSlots) * 100).toFixed(0)
      : '0';

    const overallSummaryText = `*4-Week Overall Summary*\n\n` +
      `\`\`\`\n` +
      `Type         Total Slots    Booked    Utilization\n` +
      `──────────────────────────────────────────────────\n` +
      `Lew leads    ${totalStandardSlots.toString().padEnd(13)} ${totalStandardBooked.toString().padEnd(9)} ${overallStandardUtilization}%\n` +
      `No-Show      ${totalNoShowSlots.toString().padEnd(13)} ${totalNoShowBooked.toString().padEnd(9)} ${overallNoShowUtilization}%\n` +
      `\`\`\``;

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: overallSummaryText,
      },
    });

    // Footer
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `_Automated report • ${config.devMode ? 'Development' : 'Production'} environment_`,
        },
      ],
    });

    return blocks;
  }

  /**
   * Send the formatted blocks to Slack
   */
  private async sendToSlack(blocks: (Block | KnownBlock)[]): Promise<void> {
    const slackChannel = config.slackAvailabilityReportChannel;

    if (!slackChannel) {
      const warningMsg = 'No Slack availability report channel configured (SLACK_AVAILABILITY_REPORT_CHANNEL)';
      logger.warn(warningMsg);
      throw new Error(warningMsg);
    }

    if (!config.slackToken) {
      const errorMsg = 'No Slack token configured (SLACK_TOKEN)';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      const response = await this.slack.chat.postMessage({
        channel: slackChannel,
        blocks: blocks,
        text: 'Weekly Availability Report', // Fallback text for notifications
        username: 'Availability Report Bot',
        icon_emoji: ':calendar:'
      });

      if (!response.ok) {
        throw new Error(`Slack API error: ${response.error || 'Unknown error'}`);
      }

    } catch (error) {
      logger.error('Failed to send availability report to Slack:', error);
      throw error;
    }
  }

  /**
   * Get availability data for a specific date range (for testing/manual reports)
   */
  async getAvailabilityForDateRange(startDate: string, endDate: string): Promise<DayAvailabilityData> {
    const weekStart = DateTime.fromISO(startDate).setZone('Australia/Sydney');
    const weekEnd = DateTime.fromISO(endDate).setZone('Australia/Sydney');

    const weekReport = await this.getAvailabilityForWeek(weekStart, weekEnd);
    return weekReport.weekTotal;
  }

  /**
   * Send a test report
   */
  async sendTestReport(): Promise<void> {
    try {
      await this.sendWeeklyAvailabilityReport();
    } catch (error) {
      logger.error('Failed to send test availability report:', error);
      throw error;
    }
  }

  /**
   * Send a test report with specific date range
   */
  async sendTestReportWithDates(startDate: string): Promise<void> {
    try {
      // Generate report for 4 weeks starting from the specified date
      const reports: WeeklyAvailabilityReport[] = [];
      const startDateTime = DateTime.fromISO(startDate).setZone('Australia/Sydney');
      const now = DateTime.now().setZone('Australia/Sydney');

      for (let weekNum = 1; weekNum <= 4; weekNum++) {
        const weekStart = startDateTime.plus({ weeks: weekNum - 1 }).startOf('week'); // Monday
        const weekEnd = weekStart.plus({ days: 4 }); // Friday

        // Only include weeks that are not in the past
        if (weekStart >= now.startOf('day')) {
          const weekReport = await this.getAvailabilityForWeek(weekStart, weekEnd);
          weekReport.weekNumber = weekNum;
          reports.push(weekReport);
        }
      }

      if (reports.length === 0) {
        return;
      }

      const slackBlocks = this.formatSlackBlocks(reports);
      await this.sendToSlack(slackBlocks);
    } catch (error) {
      logger.error('Failed to send test availability report with custom dates:', error);
      throw error;
    }
  }
}

export default new AvailabilityReportingService();
