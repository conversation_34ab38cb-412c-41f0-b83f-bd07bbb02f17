DROP TABLE IF EXISTS Admission CASCADE;
DROP TABLE IF EXISTS PatientQueueDetails CASCADE;
DROP TABLE IF EXISTS PatientQueue CASCADE;
DROP TABLE IF EXISTS Consultation CASCADE;
DROP TABLE IF EXISTS HealthCheck CASCADE;
DROP TABLE IF EXISTS Orders CASCADE;
DROP TABLE IF EXISTS Questionnaire CASCADE;
DROP TABLE IF EXISTS TreatmentPlan CASCADE;
DROP TABLE IF EXISTS Patient CASCADE;
DROP TABLE IF EXISTS DischargeLetters CASCADE;
DROP TABLE IF EXISTS OrderItems CASCADE;
DROP TABLE IF EXISTS PatientOrder CASCADE;
DROP TABLE IF EXISTS doctorStartEndTimer

-- DROP TABLE IF EXISTS Dr CASCADE;

-- Dr Table
CREATE TABLE IF NOT EXISTS Dr (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
email TEXT UNIQUE,
name TEXT,
username TEXT UNIQUE,
"accessID" TEXT NOT NULL UNIQUE,
status TEXT,
role TEXT,
"aphraNumber" TEXT,
"consultationDurationMinutes" INTEGER DEFAULT 6,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Patient Table
CREATE TABLE Patient (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"fullName" TEXT NOT NULL,
email TEXT NOT NULL CHECK (email = LOWER(email)),
password TEXT,
"returningPatient" BOOLEAN DEFAULT FALSE,
"patientID" VARCHAR(255) UNIQUE NOT NULL,
"zohoID" TEXT,
locked BOOLEAN DEFAULT FALSE,
"drLocked" TEXT REFERENCES Dr("accessID") ON DELETE RESTRICT,
state TEXT,
"lastCompletedForm" TEXT,
"dob" TEXT,
"usedCannabisBefore" BOOLEAN,
"mobile" TEXT,
"riskRating" INTEGER,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE (email)
);

CREATE OR REPLACE FUNCTION normalize_email()
RETURNS TRIGGER AS $$
BEGIN
NEW.email := LOWER(NEW.email);
RETURN NEW;
END;

$$
LANGUAGE plpgsql;

-- Trigger for Normalization
CREATE TRIGGER normalize_email_trigger
BEFORE INSERT OR UPDATE ON Patient
FOR EACH ROW
EXECUTE FUNCTION normalize_email();

CREATE INDEX idx_patient_patientID ON Patient("patientID");

-- DischargeLetters Table
CREATE TABLE DischargeLetters (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"fileName" TEXT,
"mimeType" TEXT,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"patientID" VARCHAR(255) NOT NULL,
"zohoID" VARCHAR(255) NOT NULL,
status TEXT,
"fileData" BYTEA, -- For binary data
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_discharge_letter_zohoid ON DischargeLetters("zohoID");
CREATE INDEX idx_discharge_letter_patientid ON DischargeLetters("patientID");

CREATE INDEX idx_dr_accessID ON Dr("accessID");

-- Consultation Table
CREATE TABLE Consultation (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255),
"drId" TEXT,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"joinedAt" TIMESTAMPTZ,
"consultationDate" TIMESTAMPTZ NOT NULL,
"meetingOngoing" BOOLEAN DEFAULT FALSE,
"drJoined" BOOLEAN DEFAULT FALSE,
"consultationStart" TIMESTAMPTZ,
"consultationEnd" TIMESTAMPTZ,
"notificationSent" BOOLEAN DEFAULT FALSE,
"notificationSentDateTime" TIMESTAMPTZ,
completed BOOLEAN DEFAULT FALSE,
"warningSent" BOOLEAN DEFAULT FALSE,
"warningSentAt" TIMESTAMPTZ,
"queueTag" TEXT,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE ("patientID", "consultationDate")
);

CREATE INDEX idx_consultation_patientID ON Consultation("patientID");

-- TreatmentPlan Table
CREATE TABLE TreatmentPlan (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"drId" TEXT REFERENCES Dr("accessID") ON DELETE SET NULL,
"consultationId" UUID REFERENCES Consultation(id) ON DELETE SET NULL,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
outcome TEXT,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"drNotes" TEXT,
date DATE,
"drName" TEXT,
"mentalHealthSupprtingDocument" TEXT,
"type" TEXT,
"dosePerDay22" NUMERIC,
"strengthAndConcentration22" TEXT,
"maxDose22" NUMERIC,
"totalQuantity22" NUMERIC,
"numberOfRepeat22" INTEGER,
"supplyInterval22" INTEGER,
"dosePerDay29" NUMERIC,
"strengthAndConcentration29" TEXT,
"maxDose29" NUMERIC,
"totalQuantity29" NUMERIC,
"numberOfRepeat29" INTEGER,
"supplyInterval29" INTEGER

);

CREATE INDEX idx_treatmentplan_patientID ON TreatmentPlan("patientID");

-- Questionnaire Table
CREATE TABLE Questionnaire (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"zohoID" TEXT NOT NULL,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
question TEXT NOT NULL,
answers TEXT NOT NULL,
"type" TEXT,
"questionnaireID" TEXT,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE ("patientID", "email", "question")
);

CREATE INDEX idx_questionnaire_patientID ON Questionnaire("patientID");

-- Order Table
CREATE TABLE Orders (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"orderNumber" UUID UNIQUE DEFAULT gen_random_uuid(),
date DATE NOT NULL,
strength TEXT,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
quantity NUMERIC,
"type" TEXT,
"remainingQuantity" NUMERIC,
"initialQuantity" NUMERIC,
"remainingRepeat" INTEGER,
"initialRepeat" INTEGER,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_orders_patientID ON Orders("patientID");

-- HealthCheck Table
CREATE TABLE HealthCheck (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"zohoID" VARCHAR(255) REFERENCES Patient("patientID") ON DELETE CASCADE,
question TEXT NOT NULL,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
answers TEXT NOT NULL,
"healthCheckID" TEXT,
"type" TEXT,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_healthcheck_patientID ON HealthCheck("patientID");

-- Patient Queue Table
CREATE TABLE PatientQueue (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"joinedAt" TIMESTAMPTZ,
status TEXT NOT NULL,
"notificationSent" BOOLEAN DEFAULT FALSE,
"notificationSentDateTime" TIMESTAMPTZ,
"leftAt" TIMESTAMPTZ,
"joinedCallAt" TIMESTAMPTZ,
"admittedAt" TIMESTAMPTZ,
"callEndedAt" TIMESTAMPTZ,
"confirmedAt" TIMESTAMPTZ,
"firstTimeJoined" TIMESTAMPTZ,
"completedAt" TIMESTAMPTZ,
"noShow" BOOLEAN DEFAULT NULL,
"attempt" INTEGER DEFAULT 0,
"allowed" BOOLEAN DEFAULT NULL,
UNIQUE ("patientID")
);

CREATE TABLE PatientQueueDetails (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"joinedAt" TIMESTAMPTZ,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
status TEXT NOT NULL,
"notificationSent" BOOLEAN DEFAULT FALSE,
"notificationSentDateTime" TIMESTAMPTZ,
"leftAt" TIMESTAMPTZ,
"joinedCallAt" TIMESTAMPTZ,
"admittedAt" TIMESTAMPTZ,
"callEndedAt" TIMESTAMPTZ,
"confirmedAt" TIMESTAMPTZ,
"completedAt" TIMESTAMPTZ,
UNIQUE ("id")
);

CREATE TABLE Admission (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"admitted" BOOLEAN DEFAULT FALSE,
"drId" TEXT REFERENCES Dr("accessID") ON DELETE SET NULL,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE ("id")
);

CREATE INDEX idx_patientqueue_patientID ON PatientQueue("patientID");

CREATE TABLE PatientOrder(
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
"orderID" TEXT,
"wpUserID" TEXT,
"zohoContactID" TEXT,
"allowanceLeft" INTEGER,
"treatmentPlanDate" TEXT,
"drName" TEXT,
"orderDateAndTime" TEXT,
"repeatLeft" INTEGER,
"allowanceLeft22" NUMERIC,
"allowanceLeft29" NUMERIC,
"repeatLeft22" INTEGER,
"repeatLeft29" INTEGER,
"type" TEXT DEFAULT 'order',
"collector" TEXT DEFAULT 'Jacqui Verdich',
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE ("orderID")
);

CREATE TABLE OrderItems (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"orderID" TEXT REFERENCES PatientOrder("orderID") ON DELETE CASCADE ON UPDATE CASCADE,
sku TEXT,
"tradeName" TEXT,
"quantity" TEXT,
strength TEXT,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
UNIQUE ("id")
);


CREATE TABLE doctorStartEndTimer (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"drId" TEXT REFERENCES Dr("accessID") ON DELETE SET NULL,
action TEXT,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
)
$$

//OTP TABLE

CREATE TABLE otps (
id SERIAL PRIMARY KEY,
otp VARCHAR(255) NOT NULL,
validated BOOLEAN DEFAULT FALSE,
active BOOLEAN DEFAULT TRUE,
expirationdate TIMESTAMP NOT NULL,
createdat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updatedat TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER PATIENT TABLE

ALTER TABLE patient
ADD COLUMN "phoneVerified" BOOLEAN DEFAULT FALSE;

ALTER TABLE otps
ADD COLUMN phone VARCHAR(255) NOT NULL;

CREATE TABLE leads (
lead_id VARCHAR(50) NOT NULL,
mobile VARCHAR(20),
phone VARCHAR(20),
full_name VARCHAR(100),
agent VARCHAR(100),
status VARCHAR(20) DEFAULT 'NEW',
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (lead_id)
);

COMMENT ON TABLE leads IS 'Table to store lead information';
COMMENT ON COLUMN leads.lead_id IS 'Unique identifier for the lead';
COMMENT ON COLUMN leads.mobile IS 'Mobile phone number of the lead';
COMMENT ON COLUMN leads.phone IS 'Landline phone number of the lead';
COMMENT ON COLUMN leads.full_name IS 'Full name of the lead';
COMMENT ON COLUMN leads.agent IS 'Auto dialer agent assigned to the lead';
COMMENT ON COLUMN leads.status IS 'Current status of the lead (NEW, CONTACTED, CONVERTED, etc.)';
COMMENT ON COLUMN leads.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN leads.updated_at IS 'Timestamp when the record was last updated';

ALTER PATIENT TABLE

ALTER TABLE patient
ADD COLUMN "phoneVerified" BOOLEAN DEFAULT FALSE;

DROP TABLE IF EXISTS "lead_status";
DROP SEQUENCE IF EXISTS lead_status_status_id_seq;
CREATE SEQUENCE lead_status_status_id_seq INCREMENT 1 MINVALUE 1 MAXVALUE ********** CACHE 1;

CREATE TABLE "public"."lead_status" (
"status_id" integer DEFAULT nextval('lead_status_status_id_seq') NOT NULL,
"status_name" character varying(50) NOT NULL,
"description" text,
"is_active" boolean DEFAULT true,
"created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT "lead_status_pkey" PRIMARY KEY ("status_id")
) WITH (oids = false);

-- Add consent_form_completed column to Patient table
ALTER TABLE "patient"
ADD COLUMN "consent_form_completed" BOOLEAN DEFAULT FALSE;

CREATE TABLE "consentsubmission" (
"id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patient_id" UUID NOT NULL REFERENCES "patient"(id) ON DELETE CASCADE,
"submission_date" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"voluntary_consent" BOOLEAN NOT NULL,
"legally_competent" BOOLEAN NOT NULL,
"sufficient_information" BOOLEAN NOT NULL,
"understanding_risks" BOOLEAN NOT NULL,
"medical_cannabis_unapproved" BOOLEAN NOT NULL,
"illegal_prescription" BOOLEAN NOT NULL,
"drug_interactions" BOOLEAN NOT NULL,
"no_use_while_treated" BOOLEAN NOT NULL,
"illegal_to_minors" BOOLEAN NOT NULL,
"signature" TEXT NOT NULL,
"ip_address" TEXT,
"device_details" TEXT,
"created_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX "idx_consent_submission_patient_id" ON "consentsubmission"("patient_id");

-- Add noShowAvailability column to Range table
ALTER TABLE Range
ADD COLUMN "noShowAvailability" INTEGER DEFAULT 0;

-- Add noShowRemaining column to Slot table
ALTER TABLE Slot
ADD COLUMN "noShowRemaining" INTEGER DEFAULT 0;

ALTER TABLE PatientSlot
ADD COLUMN "queueType" VARCHAR(20) DEFAULT 'regular';

-- Create DoctorQueue table
CREATE TABLE IF NOT EXISTS DoctorQueue (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"doctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL,
"assignedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
status VARCHAR(50) NOT NULL, -- 'ASSIGNED', 'CONSULTED', 'REASSIGNED'
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
FOREIGN KEY ("patientID") REFERENCES Patient("patientID") ON DELETE CASCADE,
UNIQUE ("patientID", "doctorID")
);

-- Add indexes for faster lookups
CREATE INDEX idx_doctorqueue_patientid ON DoctorQueue("patientID");
CREATE INDEX idx_doctorqueue_doctorid ON DoctorQueue("doctorID");

-- Modify PatientQueue table to add doctor assignment columns
ALTER TABLE PatientQueue
ADD COLUMN "assignedDoctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL,
ADD COLUMN "consultedDoctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL;

-- Add indexes for the new columns
CREATE INDEX idx_patientqueue_assigneddoctorid ON PatientQueue("assignedDoctorID");
CREATE INDEX idx_patientqueue_consulteddoctorid ON PatientQueue("consultedDoctorID");

-- Add context to otps
ALTER TABLE otps
ADD COLUMN "context" VARCHAR(20) DEFAULT 'register';

ALTER TABLE DoctorQueue
DROP CONSTRAINT doctorqueue_patientid_fkey,
ADD CONSTRAINT doctorqueue_patientid_fkey
FOREIGN KEY ("patientID") REFERENCES Patient("patientID")
ON DELETE CASCADE
ON UPDATE CASCADE;
-- Add payment flow
DROP TABLE IF EXISTS "paymentflowlogs";
CREATE TABLE "public"."paymentflowlogs" (
"state" text NOT NULL,
"os" text,
"browser" text,
"creationDate" timestamp,
"paymentLink" text,
"browserVersion" text,
"orderId" text
) WITH (oids = false);

CREATE TABLE IF NOT EXISTS doctor_sessions (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"doctorId" TEXT NOT NULL REFERENCES Dr("accessID") ON DELETE CASCADE,
"sessionId" TEXT, -- Optional session identifier from frontend
action TEXT NOT NULL CHECK (action IN ('LOGIN', 'LOGOUT')),
timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
"sessionDuration" INTEGER, -- Duration in seconds, only for logout events
"ipAddress" TEXT, -- IP address for audit purposes
"userAgent" TEXT, -- User agent string for device tracking
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_doctor_id ON doctor_sessions("doctorId");
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_action ON doctor_sessions(action);
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_timestamp ON doctor_sessions(timestamp);
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_created_at ON doctor_sessions("createdAt");

-- Create a composite index for common queries (doctor + action + timestamp)
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_doctor_action_timestamp
ON doctor_sessions("doctorId", action, timestamp);

-- Add a trigger to automatically update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_doctor_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
NEW."updatedAt" = CURRENT_TIMESTAMP;
RETURN NEW;
END;

$$
LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_doctor_sessions_updated_at
    BEFORE UPDATE ON doctor_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_doctor_sessions_updated_at();

-- Add consultation duration column to existing Dr table
ALTER TABLE Dr
ADD COLUMN IF NOT EXISTS "consultationDurationMinutes" INTEGER DEFAULT 6;


-- Create DoctorQueue table
CREATE TABLE IF NOT EXISTS DoctorQueue (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"patientID" VARCHAR(255) NOT NULL,
"doctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL,
"assignedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
status VARCHAR(50) NOT NULL, -- 'ASSIGNED', 'CONSULTED', 'REASSIGNED'
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
FOREIGN KEY ("patientID") REFERENCES Patient("patientID") ON DELETE CASCADE,
UNIQUE ("patientID", "doctorID")
);

-- Add indexes for faster lookups
CREATE INDEX idx_doctorqueue_patientid ON DoctorQueue("patientID");
CREATE INDEX idx_doctorqueue_doctorid ON DoctorQueue("doctorID");

-- Modify PatientQueue table to add doctor assignment columns
ALTER TABLE PatientQueue
ADD COLUMN "assignedDoctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL,
ADD COLUMN "consultedDoctorID" VARCHAR(255) REFERENCES Dr("accessID") ON DELETE SET NULL;

-- Add indexes for the new columns
CREATE INDEX idx_patientqueue_assigneddoctorid ON PatientQueue("assignedDoctorID");
CREATE INDEX idx_patientqueue_consulteddoctorid ON PatientQueue("consultedDoctorID");

-- Add context to otps
ALTER TABLE otps
ADD COLUMN "context" VARCHAR(20) DEFAULT 'register';

ALTER TABLE DoctorQueue
DROP CONSTRAINT doctorqueue_patientid_fkey,
ADD CONSTRAINT doctorqueue_patientid_fkey
FOREIGN KEY ("patientID") REFERENCES Patient("patientID")
ON DELETE CASCADE
ON UPDATE CASCADE;
-- Add payment flow
DROP TABLE IF EXISTS "paymentflowlogs";
CREATE TABLE "public"."paymentflowlogs" (
"state" text NOT NULL,
"os" text,
"browser" text,
"creationDate" timestamp,
"paymentLink" text,
"browserVersion" text,
"orderId" text
) WITH (oids = false);

CREATE TABLE IF NOT EXISTS doctor_sessions (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
"doctorId" TEXT NOT NULL REFERENCES Dr("accessID") ON DELETE CASCADE,
"sessionId" TEXT, -- Optional session identifier from frontend
action TEXT NOT NULL CHECK (action IN ('LOGIN', 'LOGOUT')),
timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
"sessionDuration" INTEGER, -- Duration in seconds, only for logout events
"ipAddress" TEXT, -- IP address for audit purposes
"userAgent" TEXT, -- User agent string for device tracking
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_doctor_id ON doctor_sessions("doctorId");
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_action ON doctor_sessions(action);
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_timestamp ON doctor_sessions(timestamp);
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_created_at ON doctor_sessions("createdAt");

-- Create a composite index for common queries (doctor + action + timestamp)
CREATE INDEX IF NOT EXISTS idx_doctor_sessions_doctor_action_timestamp
ON doctor_sessions("doctorId", action, timestamp);

-- Add a trigger to automatically update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_doctor_sessions_updated_at()
RETURNS TRIGGER AS

BEGIN
NEW."updatedAt" = CURRENT_TIMESTAMP;
RETURN NEW;
END;

CREATE TRIGGER trigger_update_doctor_sessions_updated_at
BEFORE UPDATE ON doctor_sessions
FOR EACH ROW
EXECUTE FUNCTION update_doctor_sessions_updated_at();

-- Add consultation duration column to existing Dr table
ALTER TABLE Dr
ADD COLUMN IF NOT EXISTS "consultationDurationMinutes" INTEGER DEFAULT 6;

-- Step 2: Create the conversation moderation table
CREATE TABLE chat_conversation_moderation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "channelId" TEXT NOT NULL UNIQUE,
    "patientId" TEXT NOT NULL,
    "treatmentPlanId" UUID REFERENCES TreatmentPlan(id) ON DELETE CASCADE,
    "patientName" TEXT,
    "moderationStatus" TEXT NOT NULL DEFAULT 'pending',
    "moderatedBy" TEXT,
    "moderatedAt" TIMESTAMPTZ,
    "moderationReason" TEXT,
    "isVisible" BOOLEAN DEFAULT FALSE,
    "createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "slackMessageTs" TEXT,
    "firstMessageAt" TIMESTAMPTZ,
    "lastMessageAt" TIMESTAMPTZ,
    "messageCount" INTEGER DEFAULT 0,
    "hasAttachments" BOOLEAN DEFAULT FALSE,
    "firstMessageText" TEXT,
    "lastMessageText" TEXT,
    "messagePreview" TEXT,
    "messageIds" TEXT[] -- Array of message IDs that were part of the original conversation -- Preview of conversation for moderation interface
);

-- Step 3: Create indexes for performance
CREATE INDEX idx_chat_conversation_moderation_channel ON chat_conversation_moderation("channelId");
CREATE INDEX idx_chat_conversation_moderation_patient ON chat_conversation_moderation("patientId");
CREATE INDEX idx_chat_conversation_moderation_treatment_plan ON chat_conversation_moderation("treatmentPlanId");
CREATE INDEX idx_chat_conversation_moderation_pending ON chat_conversation_moderation("moderationStatus", "createdAt") WHERE "moderationStatus" = 'pending';
CREATE INDEX idx_chat_conversation_moderation_status ON chat_conversation_moderation("moderationStatus");
CREATE INDEX idx_chat_conversation_moderation_created ON chat_conversation_moderation("createdAt");
CREATE INDEX idx_chat_conversation_moderation_visible ON chat_conversation_moderation("isVisible") WHERE "isVisible" = TRUE;

-- Step 4: Add table and column comments
COMMENT ON TABLE chat_conversation_moderation IS 'Stores conversation-level moderation data for patient-doctor chat channels';
COMMENT ON COLUMN chat_conversation_moderation."channelId" IS 'Stream Chat channel ID for the conversation';
COMMENT ON COLUMN chat_conversation_moderation."patientId" IS 'Patient identifier (zohoId)';
COMMENT ON COLUMN chat_conversation_moderation."treatmentPlanId" IS 'Associated treatment plan for this conversation';
COMMENT ON COLUMN chat_conversation_moderation."moderationStatus" IS 'Status: pending, approved, rejected';
COMMENT ON COLUMN chat_conversation_moderation."isVisible" IS 'Whether conversation is visible to doctors';
COMMENT ON COLUMN chat_conversation_moderation."slackMessageTs" IS 'Slack message timestamp for threading replies';
COMMENT ON COLUMN chat_conversation_moderation."firstMessageAt" IS 'Timestamp of first patient message in conversation';
COMMENT ON COLUMN chat_conversation_moderation."lastMessageAt" IS 'Timestamp of most recent patient message';
COMMENT ON COLUMN chat_conversation_moderation."messageCount" IS 'Total number of patient messages in conversation';
COMMENT ON COLUMN chat_conversation_moderation."hasAttachments" IS 'Whether any messages in conversation contain attachments';
COMMENT ON COLUMN chat_conversation_moderation."firstMessageText" IS 'Text content of the first message for moderation review';
COMMENT ON COLUMN chat_conversation_moderation."lastMessageText" IS 'Text content of the most recent message';
COMMENT ON COLUMN chat_conversation_moderation."messagePreview" IS 'Preview text showing multiple messages for moderation interface';
COMMENT ON COLUMN chat_conversation_moderation."messageIds" IS 'Array of Stream Chat message IDs that were part of the original conversation when submitted for moderation';

-- Step 5: Create helper view for conversation summaries with treatment plan info
CREATE VIEW conversation_moderation_summary AS
SELECT
    ccm.*,
    tp.outcome as treatment_outcome,
    tp."drName" as doctor_name,
    tp."createdAt" as treatment_plan_created,
    tp."drId" as doctor_id,
    tp.date as treatment_plan_date
FROM chat_conversation_moderation ccm
LEFT JOIN treatmentplan tp ON ccm."treatmentPlanId" = tp.id;

-- Step 6: Create function to automatically link conversations to treatment plans
CREATE OR REPLACE FUNCTION link_conversation_to_treatment_plan(
    p_channel_id TEXT,
    p_patient_id TEXT
) RETURNS UUID AS
$$

DECLARE
treatment_plan_id UUID;
cutoff_date TIMESTAMPTZ;
BEGIN
-- Calculate cutoff date (30 days ago) as a variable
cutoff_date := NOW() - INTERVAL '30 days';

    -- Find the most recent treatment plan for this patient
    -- Look for plans created within the last 30 days to avoid linking to very old plans
    SELECT id INTO treatment_plan_id
    FROM treatmentplan
    WHERE "patientID" = p_patient_id
    AND "createdAt" >= cutoff_date
    ORDER BY "createdAt" DESC
    LIMIT 1;

    RETURN treatment_plan_id;

END;

$$
LANGUAGE plpgsql;

-- Step 7: Create function to update conversation statistics
CREATE OR REPLACE FUNCTION update_conversation_stats(
    p_channel_id TEXT,
    p_message_text TEXT DEFAULT NULL,
    p_has_attachments BOOLEAN DEFAULT FALSE
) RETURNS VOID AS
$$

BEGIN
UPDATE chat_conversation_moderation
SET
"lastMessageAt" = CURRENT_TIMESTAMP,
"messageCount" = "messageCount" + 1,
"hasAttachments" = "hasAttachments" OR p_has_attachments,
"lastMessageText" = COALESCE(p_message_text, "lastMessageText"),
"updatedAt" = CURRENT_TIMESTAMP
WHERE "channelId" = p_channel_id;
END;

$$
LANGUAGE plpgsql;

-- Step 8: Create trigger function to automatically update timestamps
CREATE OR REPLACE FUNCTION update_conversation_moderation_timestamp() RETURNS TRIGGER AS
$$

BEGIN
NEW."updatedAt" = CURRENT_TIMESTAMP;
RETURN NEW;
END;

$$
LANGUAGE plpgsql;

-- Step 9: Create trigger for automatic timestamp updates
CREATE TRIGGER trigger_update_conversation_moderation_timestamp
    BEFORE UPDATE ON chat_conversation_moderation
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_moderation_timestamp();

-- Step 10: Create function to check if conversation should be visible to doctors
CREATE OR REPLACE FUNCTION is_conversation_visible_to_doctors(p_channel_id TEXT) RETURNS BOOLEAN AS
$$

DECLARE
moderation_status TEXT;
is_visible BOOLEAN;
BEGIN
SELECT "moderationStatus", "isVisible"
INTO moderation_status, is_visible
FROM chat_conversation_moderation
WHERE "channelId" = p_channel_id;

    -- If no moderation record exists, assume it's a doctor-initiated conversation (visible)
    IF NOT FOUND THEN
        RETURN TRUE;
    END IF;

    -- Only show approved conversations
    RETURN moderation_status = 'approved' AND is_visible;

END;

$$
LANGUAGE plpgsql;

-- Step 11: Create indexes for the helper functions
-- Note: Removed time-based partial index as CURRENT_TIMESTAMP is not immutable
CREATE INDEX IF NOT EXISTS idx_treatment_plan_patient_created
ON treatmentplan("patientID", "createdAt");

-- Step 12: Add constraints to ensure data integrity
ALTER TABLE chat_conversation_moderation
ADD CONSTRAINT chk_moderation_status
CHECK ("moderationStatus" IN ('pending', 'approved', 'rejected'));

ALTER TABLE chat_conversation_moderation
ADD CONSTRAINT chk_message_count_positive
CHECK ("messageCount" >= 0);

-- Step 13: Create doctor chat activity tracking table
CREATE TABLE doctor_chat_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "channelId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL REFERENCES Dr("accessID") ON DELETE CASCADE,
    "activityType" TEXT NOT NULL CHECK ("activityType" IN ('viewed', 'read_messages', 'sent_message', 'submitted_treatment_plan')),
    "activityTimestamp" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "messageId" TEXT, -- For message-specific activities
    "treatmentPlanId" UUID REFERENCES TreatmentPlan(id), -- For treatment plan submissions
    "metadata" JSONB, -- Additional activity data
    "createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Step 14: Create indexes for doctor chat activity table
CREATE INDEX idx_doctor_chat_activity_channel ON doctor_chat_activity("channelId");
CREATE INDEX idx_doctor_chat_activity_doctor ON doctor_chat_activity("doctorId");
CREATE INDEX idx_doctor_chat_activity_type ON doctor_chat_activity("activityType");
CREATE INDEX idx_doctor_chat_activity_timestamp ON doctor_chat_activity("activityTimestamp");

-- Step 15: Add table and column comments for doctor chat activity
COMMENT ON TABLE doctor_chat_activity IS 'Tracks doctor interactions with patient chat conversations for admin visibility';
COMMENT ON COLUMN doctor_chat_activity."channelId" IS 'Stream Chat channel ID for the conversation';
COMMENT ON COLUMN doctor_chat_activity."doctorId" IS 'Doctor identifier from Dr.accessID';
COMMENT ON COLUMN doctor_chat_activity."activityType" IS 'Type of activity: viewed, read_messages, sent_message, submitted_treatment_plan';
COMMENT ON COLUMN doctor_chat_activity."activityTimestamp" IS 'When the activity occurred';
COMMENT ON COLUMN doctor_chat_activity."messageId" IS 'Specific message ID for message-related activities';
COMMENT ON COLUMN doctor_chat_activity."treatmentPlanId" IS 'Treatment plan ID for treatment plan submissions';
COMMENT ON COLUMN doctor_chat_activity."metadata" IS 'Additional activity data in JSON format';

-- Step 13: Create initial data validation
-- Ensure that approved conversations are visible and rejected ones are not
CREATE OR REPLACE FUNCTION validate_conversation_visibility() RETURNS TRIGGER AS
$$

BEGIN
-- Auto-set visibility based on moderation status
IF NEW."moderationStatus" = 'approved' THEN
NEW."isVisible" = TRUE;
ELSIF NEW."moderationStatus" = 'rejected' THEN
NEW."isVisible" = FALSE;
END IF;

    RETURN NEW;

END;

$$
LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_conversation_visibility
    BEFORE INSERT OR UPDATE ON chat_conversation_moderation
    FOR EACH ROW
    EXECUTE FUNCTION validate_conversation_visibility();

ALTER TABLE range ADD CONSTRAINT fk_doctor
    FOREIGN KEY ("doctorID")
    REFERENCES dr("id")
    ON DELETE SET NULL;

ALTER TABLE "patientqueue"
        ADD COLUMN "consultationId" UUID,
        ADD CONSTRAINT fk_consultation
        FOREIGN KEY ("consultationId")
        REFERENCES "consultation"(id);

DROP INDEX IF EXISTS "PatientQueue_patientID_consultationId_idx";

ALTER TABLE "patientqueue"
    ADD CONSTRAINT "PatientQueue_patientID_consultationId_unique"
    UNIQUE ("patientID", "consultationId");


DROP TABLE IF EXISTS "productstock";
CREATE TABLE "public"."productstock" (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "name" text NOT NULL,
    "code" text NOT NULL,
    "balance" integer NOT NULL
) WITH (oids = false);


-- Add the initials column
ALTER TABLE Dr ADD COLUMN initials VARCHAR(10);

-- Update the initials based on the name field (assuming format "First Last")
UPDATE Dr
SET initials = UPPER(
  COALESCE(LEFT(TRIM(SPLIT_PART(name, ' ', 1)), 1), '') ||
  COALESCE(LEFT(TRIM(SPLIT_PART(name, ' ', 2)), 1), '')
)
WHERE name IS NOT NULL AND name != '';

-- For any records where name is null/empty, try to use username
UPDATE Dr
SET initials = UPPER(
  COALESCE(LEFT(TRIM(SPLIT_PART(username, ' ', 1)), 1), '') ||
  COALESCE(LEFT(TRIM(SPLIT_PART(username, ' ', 2)), 1), '')
)
WHERE (initials IS NULL OR initials = '')
AND username IS NOT NULL AND username != '';

-- Set a default for any remaining null values
UPDATE Dr
SET initials = 'DR'
WHERE initials IS NULL OR initials = '';


ALTER TABLE PatientSlot
ADD COLUMN IF NOT EXISTS "bookedByAdminId" TEXT,
ADD COLUMN IF NOT EXISTS "bookingType" VARCHAR(20) DEFAULT 'patient';

-- Add foreign key constraint to link to Dr table
ALTER TABLE PatientSlot
ADD CONSTRAINT fk_booked_by_admin
    FOREIGN KEY ("bookedByAdminId")
    REFERENCES Dr("accessID")
    ON DELETE SET NULL;

-- Add constraint to ensure bookingType has valid values
ALTER TABLE PatientSlot
ADD CONSTRAINT chk_booking_type
CHECK ("bookingType" IN ('patient', 'admin'));

-- Add index for faster lookups by admin
CREATE INDEX IF NOT EXISTS idx_patient_slot_admin_booking
ON PatientSlot("bookedByAdminId")
WHERE "bookedByAdminId" IS NOT NULL;

-- Add index for booking type
CREATE INDEX IF NOT EXISTS idx_patient_slot_booking_type
ON PatientSlot("bookingType");

-- Add comments for documentation
COMMENT ON COLUMN PatientSlot."bookedByAdminId" IS 'ID of the admin who booked this appointment (NULL for patient bookings)';
COMMENT ON COLUMN PatientSlot."bookingType" IS 'Type of booking: patient (self-booked) or admin (booked by admin)';
$$

ALTER TABLE patientqueue ADD COLUMN tech_issue BOOLEAN DEFAULT false;

ALTER TABLE TreatmentPlan
ADD COLUMN "source" TEXT NOT NULL DEFAULT 'consultation';

-- Add comment to document the purpose of this column
COMMENT ON COLUMN TreatmentPlan."source" IS 'Source of treatment plan creation: consultation (normal consultations), messenger (chat, requests, approvals via messaging)';

-- Create index for better query performance when filtering by source
CREATE INDEX IF NOT EXISTS idx_treatmentplan_source
ON TreatmentPlan("source");

ALTER TABLE TreatmentPlan
ADD CONSTRAINT chk_treatmentplan_source
CHECK ("source" IN ('consultation', 'messenger'));

CREATE INDEX IF NOT EXISTS idx_consultation_created_at ON Consultation("createdAt");
