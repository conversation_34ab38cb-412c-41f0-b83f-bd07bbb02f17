import routes from "./apiRoutes";
import axiosInstance from "./axios";
import chatService from "./chat.service";
import {
  Admission,
  AvailabilityEntry,
  AvailableDate,
  ConsentFormData,
  ConsentFormResponse,
  ConsentFormStatusResponse,
  ConsultationFilter,
  Dr,
  DrActivity,
  HealthCheckShape,
  OrderHistory,
  PatientConsultation,
  PatientData,
  PatientOrder,
  PatientQueue,
  PatientQueueResponse,
  PatientsFullHistory,
  PatientTreatmentPlan,
  Questionnaire,
  QuestionnaireShape,
  Slot,
  Timer,
  TimeStarter,
  TreatmentPlanHistory,
  DoctorSession,
  DoctorLoginRequest,
  DoctorLogoutRequest,
  ReportData,
} from "../types";
import { config } from "../config";
import moxios from "./mockBackend";

// const clientAPI = axiosInstance;
const clientAPI = config.shouldMockBackend ? moxios : axiosInstance;
interface ApiResponse<T> {
  message: string;
  status: number;
  data: T;
  error: boolean | undefined;
  success: boolean | undefined;
}
class Api {
  async getPatients() {
    return await clientAPI
      .get<PatientData[]>(routes.GET_PATIENTS)
      .then((res) => res.data);
  }
  async getPatientsRedis() {
    return await clientAPI
      .get<PatientData[]>(routes.GET_PATIENTS_REDIS)
      .then((res) => res.data);
  }

  async postTreamentPlan(data: PatientTreatmentPlan) {
    return await clientAPI
      .post<PatientData>(routes.POST_PATIENT_TP, data)
      .then((res) => res.data);
  }

  async postChatTreatmentPlan(data: PatientTreatmentPlan) {
    return await clientAPI
      .post<PatientData>(routes.POST_CHAT_TREATMENT_PLAN, data)
      .then((res) => res.data);
  }

  async postEmail(email: string) {
    return await clientAPI
      .post<string>(routes.POST_EMAIL, email, {
        headers: {
          "Content-Type": "text/html",
        },
      })
      .then((res) => res.data);
  }
  async updatePatientLockedStatus(id: string, status: boolean, dr: string) {
    return await clientAPI
      .put<PatientData>(`${routes.UPDATE_LOCK}/${id}`, {
        status,
        dr,
      })
      .then((res) => res.data);
  }

  async verifyLockedPatient(id: string) {
    return await clientAPI
      .get<PatientData>(`${routes.GET_LOCKED}/${id}`)
      .then((res) => res.data);
  }

  async getDoctorBySupabaseId(id: string) {
    return await clientAPI
      .get<Dr>(`${routes.GET_DOCTOR}/${id}`)
      .then((res) => res.data);
  }

  async getDoctors() {
    return await clientAPI
      .get<Dr[]>(`${routes.GET_ALL_DOCTOR}`)
      .then((res) => res.data);
  }

  async getActiveDoctors() {
    return await clientAPI
      .get<Dr[]>(`${routes.GET_ACTIVE_DOCTORS}`)
      .then((res) => res.data);
  }

  async postDoctorData(data: Dr) {
    return await clientAPI
      .post<Dr>(`${routes.POST_DOCTOR}`, data)
      .then((res) => res.data);
  }

  async getNextPatient() {
    return await clientAPI
      .get<Dr>(`${routes.GET_NEXT_PATIENT}`)
      .then((res) => res.data);
  }

  async notifyNextPatient(doctorId: string) {
    return await clientAPI
      .post<PatientData>(`${routes.POST_NOTIFY_NEXT_PATIENT}`, { doctorId })
      .then((res) => res.data);
  }

  async updateMeetingStatus(token: string, status: boolean) {
    return await clientAPI
      .put<
        PatientConsultation[]
      >(`${routes.UPDATE_MEETING_STATUS}/${token}`, { status })
      .then((res) => res.data);
  }

  async postAvailabilities(ranges: AvailabilityEntry[]) {
    return await clientAPI
      .post<AvailabilityEntry[]>(`${routes.POST_AVAILABILITES}`, ranges)
      .then((res) => res.data);
  }

  async deleteAvailability(entry: AvailableDate) {
    return await clientAPI
      .delete<
        AvailableDate[]
      >(`${routes.DELETE_AVAILABILITES}/${entry.range_id}`)
      .then((res) => res.data);
  }

  async deleteAvailabilityForce(entry: AvailableDate) {
    return await clientAPI
      .delete<
        AvailableDate[]
      >(`${routes.DELETE_AVAILABILITES_FORCE}/${entry.range_id}`)
      .then((res) => res.data);
  }

  async getAvailabilitiesForPatient(doctorId?: string) {
    const url = doctorId 
      ? `${routes.GET_AVAILABILITES}?doctorID=${doctorId}`
      : routes.GET_AVAILABILITES;
      
    return await clientAPI
      .get<AvailableDate[]>(url)
      .then((res) => res.data);
  }

  async getAvailabilitiesForAdmin() {
    return await clientAPI
      .get<AvailableDate[]>(`${routes.GET_AVAILABILITES_ADMIN}`)
      .then((res) => res.data);
  }

  async postPatientBooking(leadID: string, slot: Slot & { doctorID?: string }, rebookFlag?: string, adminId?: string) {
    const bookingData = adminId ? { ...slot, bookedByAdminId: adminId } : slot;
    return await clientAPI
      .post<Slot[]>(`${routes.POST_SLOT}/${leadID}/${rebookFlag ? rebookFlag : undefined}`, bookingData)
      .then((res) => res.data);
  }

  async getPatientBookingHistory(patientId: string) {
    return await clientAPI
      .get<any[]>(`${routes.GET_BOOKING_HISTORY}/${patientId}`)
      .then((res) => res.data);
  }

  async getAdminBookings(params?: { adminId?: string; startDate?: string; endDate?: string; period?: string; page?: number; limit?: number }) {
    return await clientAPI
      .get<{
        bookings: {
          patient_id: string;
          range_date: string;
          slot_details: string;
          admin_name: string;
          admin_email: string;
          bookedByAdminId: string;
          createdAt: string;
          updatedAt: string;
        }[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
          hasNext: boolean;
          hasPrev: boolean;
        };
      }>(routes.GET_ADMIN_BOOKINGS, { params })
      .then((res) => res.data);
  }

  async getDecryptedUrl(leadID: string) {
    return await clientAPI
      .post<{ leadID: string }>(`${routes.GET_LEAD_ID}`, { leadID })
      .then((res) => {
        if (res.data.leadID) {
          return res.data.leadID;
        } else {
          return undefined;
        }
      });
  }
  async postRedirect(patient: PatientData) {
    return await clientAPI
      .post<PatientData>(`${routes.POST_REDIRECT}`, patient)
      .then((res) => res.data);
  }

  async postPatientWaitingQueue(id: string) {
    return await clientAPI
      .post<PatientData>(`${routes.QUEUE}`, { patientID: id })
      .then((res) => res.data);
  }

  async postPatientOffline(id: string) {
    return await clientAPI
      .post<PatientData>(`${routes.OFFLINE_QUEUE}`, { patientID: id })
      .then((res) => res.data);
  }

  async leftPatientWaitingQueue(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.LEFT_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async completedPatientWaitingQueue(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.COMPLETED_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async confirmedPatientWaitingQueue(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.CONFIRMED_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async callEndedPatientWaitingQueue(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.ENDED_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async joinedCallPatientWaitingQueue(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.JOINED_CALL_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async admittedPatient(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.JOINED_CALL_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async fetchAdmittedPatient(id: string) {
    return await clientAPI
      .put<PatientData>(`${routes.JOINED_CALL_QUEUE}/${id}`)
      .then((res) => res.data);
  }

  async fetchQueue() {
    return await clientAPI
      .get<PatientQueueResponse>(`${routes.QUEUE}`)
      .then((res) => res.data);
  }

  async fetchQueueDetails(id: string) {
    return await clientAPI
      .get<PatientQueue[]>(`${routes.QUEUE_DETAILS_ID}/${id}`)
      .then((res) => res.data);
  }

  async fetchNextPatientToConsult(doctorID: string) {
    return await clientAPI
      .get<
        ApiResponse<PatientQueue[]>
      >(`${routes.NEXTADMIT_PATIENT}?doctorID=${doctorID}`)
      .then((res) => res.data.data);
  }

  async fetchOnlineQueue() {
    return await clientAPI
      .get<PatientQueue[]>(`${routes.ONLINE_QUEUE}`)
      .then((res) => res.data);
  }

  async fetchJoinedQueue() {
    return await clientAPI
      .get<PatientQueue[]>(`${routes.JOINED_QUEUE}`)
      .then((res) => res.data);
  }

  async updateAdmission(id: string, status: boolean) {
    return await clientAPI
      .put<Admission>(`${routes.ADMISSION}/${id}`, { status })
      .then((res) => res.data);
  }

  async postPatientAdmission(patientID: string, drID: string) {
    return await clientAPI
      .post<Admission>(`${routes.ADMISSION}`, { patientID, drID })
      .then((res) => res.data);
  }

  async fetchLatestAdmission() {
    return await clientAPI
      .get<Admission[]>(`${routes.ADMISSION}`)
      .then((res) => res.data);
  }

  async emptyQueue() {
    return await clientAPI
      .delete<[]>(`${routes.EMPTY_QUEUE}`)
      .then((res) => res.data);
  }

  async getPatientById(id: string) {
    return await clientAPI
      .get<PatientData>(`${routes.GET_PATIENT}/${id}`)
      .then((res) => res.data);
  }

  async getPatientByZohoId(id: string) {
    return await clientAPI
      .get<PatientData>(`${routes.GET_PATIENT_BY_ZOHO_ID}/${id}`)
      .then((res) => res.data);
  }

  async getPatientByIdFromDB(patientId: string) {
    return await clientAPI
      .get<PatientData>(`${routes.GET_PATIENT_BY_PATIENT_ID}/${patientId}`)
      .then((res) => res.data);
  }

  async getContactDetailsByZohoId(zohoId: string) {
    return await clientAPI
      .get(`${routes.GET_ZOHO_CONTACT_DETAILS}/${zohoId}`)
      .then((res) => res.data)
      .catch((error) => {
        console.error("Error fetching Zoho contact details:", error);
        return null;
      });
  }

  async updatePatientZohoIdByEmail(email: string, zohoId: string) {
    return await clientAPI
      .put(`${routes.UPDATE_PATIENT_ZOHO_ID}`, { email, zohoId })
      .then((res) => res.data.success)
      .catch((error) => {
        console.error("Error updating patient zohoId:", error);
        return false;
      });
  }

  async createPatientFromZohoContact(zohoId: string) {
    return await clientAPI
      .post(`/zoho/v1.0/contacts/${zohoId}/create-patient`)
      .then((res) => res.data)
      .catch((error) => {
        console.error("Error creating patient from Zoho contact:", error);
        throw error;
      });
  }

  async getPatientsFullHistory(searchParams: string) {
    return await clientAPI
      .post<
        PatientsFullHistory[]
      >(`${routes.PATIENT_HISTORY}`, { searchParams })
      .then((res) => res.data);
  }

  async getPatientsBySearchTerm(searchParams: string) {
    return await clientAPI
      .post<PatientData[]>(`${routes.GET_SEARCHED_PATIENT}`, { searchParams })
      .then((res) => res.data);
  }

  async deleteTimer(drId: string) {
    return await clientAPI
      .delete<TimeStarter>(`${routes.TIMER}/delete/${drId}`)
      .then((res) => res.data);
  }

  async storeTimer(timerId: NodeJS.Timeout) {
    return await clientAPI
      .post<Timer>(`${routes.TIMER}/store`, { timerId })
      .then((res) => res.data);
  }

  async startTimer(drId: string) {
    return await clientAPI
      .post<TimeStarter>(`${routes.TIMER}/start`, { drId })
      .then((res) => res.data);
  }

  async fetchTimer(drId: string) {
    return await clientAPI
      .get<TimeStarter>(`${routes.TIMER}/retrieve/${drId}`)
      .then((res) => res.data);
  }

  async fetchTimerAdmin() {
    return await clientAPI
      .get<{ [key: string]: string }>(`${routes.TIMER}/all/retrieve`)
      .then((res) => res.data);
  }

  async fetchAllInbox(page: number) {
    return await clientAPI
      .get<
        (TreatmentPlanHistory | OrderHistory | Questionnaire)[]
      >(`${routes.INBOX}/retrieve?page=${page}`)
      .then((res) => res.data);
  }

  async fetchInboxByEmail(email: string, page: number) {
    return await clientAPI
      .get<
        (TreatmentPlanHistory | OrderHistory | Questionnaire)[]
      >(`${routes.INBOX}/${email}/retrieve?page=${page}`)
      .then((res) => res.data);
  }

  async fetchConsultationByDate(date: string) {
    return await clientAPI
      .post<
        ConsultationFilter[]
      >(`${routes.GET_CONSULTATION_BY_DATE}`, { date })
      .then((res) => res.data);
  }

  async alertAwayPatientOnAdmit(patient: PatientData) {
    return await clientAPI
      .post<PatientData>(`${routes.NOTIFY_AWAY_PATIENT_ON_ADMIT}`, { patient })
      .then((res) => res.data);
  }

  async checkQueueEligibility(patientID: string) {
    return await clientAPI
      .get<
        TreatmentPlanHistory[]
      >(`${routes.GET_QUEUE_ELIGIBILITY}/${patientID}`)
      .then((res) => res.data);
  }

  async getPatientDoctorInfo(patientID: string) {
    return await clientAPI
      .get<{consultationDurationMinutes: number, doctorName: string | null, doctorAccessID: string | null}>(`${routes.GET_PATIENT_DOCTOR_INFO}/${patientID}/doctor-info`)
      .then((res) => res.data);
  }

  // TODO: Change All service response to this.
  async getNextPatientAutomatically(drId: string) {
    return await clientAPI
      .get<
        ApiResponse<PatientData>
      >(`${routes.GET_NEXT_PATIENT_AUTOMATICALLY}/${drId}`)
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchTreatmentPlanByEmail(email: string) {
    return await clientAPI
      .get<ApiResponse<TreatmentPlanHistory[]>>(`${routes.GET_TP}/${email}`)
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchTreatmentPlanByPatientId(patientId: string) {
    return await clientAPI
      .get<
        ApiResponse<TreatmentPlanHistory[]>
      >(routes.GET_CHAT_TREATMENT_PLANS.replace(":patientId", patientId))
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchLatestTreatmentPlanByPatientId(patientId: string) {
    return await clientAPI
      .get<
        ApiResponse<TreatmentPlanHistory | null>
      >(routes.GET_CHAT_LATEST_TREATMENT_PLAN.replace(":patientId", patientId))
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchLatestTreatmentPlanWithDoctorByPatientId(patientId: string) {
    return await clientAPI
      .get<
        ApiResponse<{
          treatmentPlan: TreatmentPlanHistory | null;
          doctor: Dr | null;
        }>
      >(
        routes.GET_CHAT_LATEST_TREATMENT_PLAN_WITH_DOCTOR.replace(
          ":patientId",
          patientId
        )
      )
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchPatientOrdersByEmail(email: string) {
    return await clientAPI
      .get<ApiResponse<PatientOrder[]>>(`${routes.GET_ORDERS}/${email}`)
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchQuestionnaireByEmail(patientID: string) {
    return await clientAPI
      .get<
        ApiResponse<QuestionnaireShape[]>
      >(`${routes.GET_QUESTIONNAIRE}/${patientID}`)
      .then((res) => {
        return res.data.data;
      });
  }

  async fetchHealthCheckByEmail(patientID: string) {
    return await clientAPI
      .get<
        ApiResponse<HealthCheckShape[]>
      >(`${routes.GET_HEALTHCHECK}/${patientID}`)
      .then((res) => {
        return res.data.data;
      });
  }

  async updatePatientToNoShowInQueue(data: PatientData | { patientID: string }, drId: string) {
    // If full PatientData object is passed, just extract the patientID to minimize payload
    const minimalData = { patientID: data.patientID };
    
    return await clientAPI
      .post<ApiResponse<PatientData>>(`${routes.POST_NOSHOW}`, { data: minimalData, drId })
      .then((res) => {
        return res.data.data;
      });
  }

  async updatePatientWithTechIssue(data: PatientData | { patientID: string }, drId: string) {
    // If full PatientData object is passed, just extract the patientID to minimize payload
    const minimalData = { patientID: data.patientID };

    
    return await clientAPI
      .post<ApiResponse<PatientData>>(`${routes.TECH_ISSUE}`, { data: minimalData, drId })
      .then((res) => {
        return res.data.data;
      });
  }

  async doctersActivity(params: {
    fullName?: string;
    email?: string;
    createdAt?: string;
    updatedAt?: string;
  }) {
    try {
      const response = await clientAPI.get<ApiResponse<DrActivity[]>>(
        routes.GET_DOCTER_ACTIVITY,
        { params: params }
      );

      return Array.isArray(response.data.data) ? response.data.data : [];
    } catch (error) {
      console.error("API fetch error:", error);
      throw error;
    }
  }

  async checkNoShow(patientID: string) {
    return await clientAPI
      .post<
        ApiResponse<PatientData[]>
      >(`${routes.NO_SHOW}`, { patientID: patientID })
      .then((res) => res.data.data);
  }

  async sendPatientToDoctor(id: string) {
    return await clientAPI
      .post<PatientData>(`${routes.STATUS_CHANGE}`, { patientID: id })
      .then((res) => res.data);
  }

  async createPrescriptionLead(zohoId: string, status?: string) {
    return await clientAPI
      .post<{
        success: boolean;
        data: { leadId: string };
      }>(routes.CREATE_PRESCRIPTION_LEAD, { patientId: zohoId, status })
      .then((res) => res.data);
  }

  async userActions(
    origin: "Doctor" | "Patient",
    target: string,
    action: string,
    comment?: string
  ) {
    return await clientAPI
      .post<PatientData>(`${routes.USER_ACTIONS}`, {
        target: target,
        origin: origin,
        action: action,
        comment: comment,
      })
      .then((res) => res.data);
  }

  async fetchPatientsWithExceededAttempts() {
    return await clientAPI
      .get<PatientQueue[]>(`${routes.EXCEEDED_ATTEMPTS}`)
      .then((res) => res.data);
  }

  async resetPatientAttempts(patientId: string) {
    return await clientAPI
      .put<PatientQueue>(`${routes.RESET_ATTEMPTS}/${patientId}`)
      .then((res) => res.data);
  }

  async sendReportToSlack(drId?: string) {
    return await clientAPI
      .post<{}>(`${routes.SEND_SLACK_REPORT}`, { drId })
      .then((res) => res.data);
  }

    async sendDetailedReportToSlack() {
    return await clientAPI
      .post<{}>(`${routes.SEND_SLACK_DETAILED_REPORT}`)
      .then((res) => res.data);
  }

  /**
   * Submit a consent form for the patient
   * @param consentData The consent form data
   * @returns The response from the API
   */
  async submitConsentForm(consentData: ConsentFormData) {
    return await clientAPI
      .post<ConsentFormResponse>(routes.SUBMIT_CONSENT_FORM, consentData)
      .then((res) => res.data);
  }

  /**
   * Get the consent form status for the current patient
   * @returns The consent form status
   */
  async getConsentFormStatus() {
    return await clientAPI
      .get<ConsentFormStatusResponse>(routes.GET_CONSENT_FORM_STATUS)
      .then((res) => res.data);
  }

  /**
   * Update the member status of a Zoho lead
   * @param leadId The Zoho lead ID
   * @param memberStatus Optional member status, defaults to "8 - Booking Page Reached"
   * @returns The response from the API
   */
  async updateZohoLeadMemberStatus(leadId: string, memberStatus?: string) {
    return await clientAPI
      .post(routes.UPDATE_ZOHO_LEAD_MEMBER_STATUS, { leadId, memberStatus })
      .then((res) => res.data);
  }

  async updateLastDoctorMessage(
    zohoId: string,
    messageSnippet?: string,
    clear?: boolean
  ) {
    return await clientAPI
      .post(routes.LAST_DOCTOR_MESSAGE, { zohoId, messageSnippet, clear })
      .then((res) => res.data);
  }

  async trackDoctorLogin(data: DoctorLoginRequest) {
    return await clientAPI
      .post<{
        success: boolean;
        data: DoctorSession;
        message: string;
      }>(routes.DOCTOR_LOGIN, data)
      .then((res) => res.data);
  }

  async trackDoctorLogout(data: DoctorLogoutRequest) {
    return await clientAPI
      .post<{
        success: boolean;
        data: DoctorSession;
        message: string;
      }>(routes.DOCTOR_LOGOUT, data)
      .then((res) => res.data);
  }

  async getDoctorSessionHistory(
    doctorId: string,
    params?: {
      startDate?: string;
      endDate?: string;
      limit?: number;
      offset?: number;
    }
  ) {
    const queryParams = new URLSearchParams();
    if (params?.startDate) queryParams.append("startDate", params.startDate);
    if (params?.endDate) queryParams.append("endDate", params.endDate);
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.offset) queryParams.append("offset", params.offset.toString());

    const url = `${routes.DOCTOR_SESSIONS}/${doctorId}${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    return await clientAPI
      .get<{
        success: boolean;
        data: DoctorSession[];
        pagination: {
          total: number;
          limit: number;
          offset: number;
          hasMore: boolean;
        };
      }>(url)
      .then((res) => res.data);
  }

  async getDoctorOnlineStatus() {
    return await clientAPI
      .get<{
        success: boolean;
        data: Array<{
          doctorId: string;
          doctorName: string;
          isOnline: boolean;
          lastActivity: string | null;
          sessionId: string | null;
          loggedInToday: boolean;
        }>;
      }>(routes.DOCTOR_STATUS)
      .then((res) => res.data);
  }

  async verifyPin(pin: string) {
    return await clientAPI
      .post<{ found: boolean }>(routes.VERIFY_PIN, { pin })
      .then((res) => res.data);
  }

  async getmedicineRegisterData(update = false) {
    return await clientAPI
      .post(routes.MEDICINE_REGISTER, {update})
      .then((res) => res.data)
      .catch((error) => {
        console.error("Couldn't fetch Medicine register data", error.message);
        return false;
      });
  }

  async getPatientStatus(patientId: string) {
    return await clientAPI
      .get<PatientQueue>(`${routes.GET_PATIENT_STATUS}/${patientId}`)
      .then((res) => res.data);
  }
  
  async getBatchPatientStatus(patientIDs: string[]) {
    return await clientAPI
      .post<{success: boolean, data: Record<string, boolean>}>(routes.GET_BATCH_PATIENT_STATUS, { patientIDs })
      .then((res) => res.data.data);
  }
  
  async getPatientDetailedStatus(patientId: string) {
    return await clientAPI
      .get<PatientQueue>(`${routes.GET_PATIENT_DETAILED_STATUS}/${patientId}`)
      .then((res) => res.data);
  }

  // Request management methods
  async getPendingRequests(includeAll: boolean = false) {
    const params = includeAll ? '?includeAll=true' : '';
    return await clientAPI
      .get<ApiResponse<{ requests: any[], counts: any }>>(routes.GET_PENDING_REQUESTS + params)
      .then((res) => res.data);
  }

  // Doctor-specific pending requests (filtered by doctor)
  async getDoctorPendingRequests(includeAll: boolean = false) {
    const params = includeAll ? '?includeAll=true' : '';
    return await clientAPI
      .get<ApiResponse<{ requests: any[], counts: any }>>(routes.GET_DOCTOR_PENDING_REQUESTS + params)
      .then((res) => res.data);
  }

  async approveRequest(requestId: string, data: { doctorId: string, doctorName: string }) {
    return await clientAPI
      .put<ApiResponse<{ id: string, status: string }>>(`${routes.APPROVE_REQUEST}/${requestId}/approve`, data)
      .then((res) => res.data);
  }

  async rejectRequest(requestId: string, data: { rejectionNotes: string, doctorId: string }) {
    return await clientAPI
      .put<ApiResponse<{ id: string, status: string }>>(`${routes.REJECT_REQUEST}/${requestId}/reject`, data)
      .then((res) => res.data);
  }
  	// Get all the AI responses in the database
	async getAICheckResponses(limit: number = 10, offset: number = 0) {
		return await clientAPI
			.get<{
				data: Partial<ReportData[]>;
				pagination: { total: number; limit: number; offset: number; hasMore: boolean };
			}>(`${routes.GET_AI_CHECK_RESPONSES}?limit=${limit}&offset=${offset}`)
			.then((res) => res)
			.catch((error) => {
				console.error("Error fetching AI check responses:", error);
				throw new Error("Failed to fetch AI check responses");
			});
	}

	// Get AI response by ID
	async getAICheckResponseByPatientId(id: string) {
		return await clientAPI
			.get<ReportData[]>(`${routes.GET_AI_CHECK_RESPONSES}/${id}`)
			.then((res) => res.data[0])
			.catch((error) => {
				console.error("Error fetching AI check response by ID:", error);
				throw new Error("Failed to fetch AI check response");
			});
	}
  async getAICheckResponseByZohoId(id: string) {
		return await clientAPI
			.get<ReportData[]>(`${routes.GET_AI_CHECK_RESPONSES}/${id}?source=zoho`)
			.then((res) => res.data[0])
			.catch((error) => {
				console.error("Error fetching AI check response by Zoho ID:", error);
				throw new Error("Failed to fetch AI check response");
			});
	}

}

export const ApiClient = new Api();
export { chatService };
