import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Dr, <PERSON>ient<PERSON><PERSON> } from "../types";
import { AxiosError } from "axios";
import ApiErrorMessage from "../components/error/apiErrorMessage";
import { SupabaseClient } from "../services/supabase-client";
import { AuthError, Session } from "@supabase/supabase-js";
import { useNavigate } from "@tanstack/react-location";
//import { _useLocation, useNavigate } from "@tanstack/react-location";
import LoadingScreen from "../utils/loading-screen";
import { ApiClient } from "../services";
import Grid from "@mui/material/Grid2";
import { getUserIP, getUserAgent, generateSessionId } from "../utils";


interface AuthContextValue {
    user: AppUser | undefined;
    session: Session | undefined;
    error: Error | null;
    login: (email?: string, password?: string) => Promise<void>;
    logout: (intervalID?: NodeJS.Timeout) => Promise<void>;
    setError: React.Dispatch<React.SetStateAction<Error | AxiosError<unknown, AppUser> | AuthError | null>>
    doctor: Dr | undefined;
    patients: PatientData[] | [] | undefined;
    setPatient: (value: React.SetStateAction<PatientData[] | [] | undefined>) => void;
}

interface AuthProviderProps {
    children: React.ReactNode;
}

const AuthContext = React.createContext<AuthContextValue | null>(null);

export const AuthProvider = ({ children }: AuthProviderProps): JSX.Element => {
    const [patients, setPatient] = useState<PatientData[] | []>()
    const [user, setUser] = useState<AppUser | undefined>(undefined);
    const [error, setError] = useState<AxiosError<unknown, AppUser> | Error | AuthError | null>(null);
    const [session, setSession] = useState<Session | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);
    const [doctor, setDoctor] = useState<Dr | undefined>(undefined)
    const [sessionId, setSessionId] = useState<string | undefined>(undefined);
    const [loginTime, setLoginTime] = useState<Date | undefined>(undefined);
    const navigate = useNavigate();
   //const location = useLocation()

    // Function to track doctor login
    const trackLogin = async (doctorData: Dr) => {
        try {
            // Only track sessions for doctors, not admins
            if (doctorData.role !== 'doctor') {
                console.log('Skipping session tracking for non-doctor user:', doctorData.role);
                return;
            }

            if(doctorData.role === 'doctor' || doctorData.role === 'superAdmin') {
                ApiClient.getPatients();
            }else{
                ApiClient.getPatientsRedis();
            }

            const newSessionId = generateSessionId();
            const ipAddress = await getUserIP();
            const userAgent = getUserAgent();


            await ApiClient.trackDoctorLogin({
                doctorId: doctorData.accessID,
                sessionId: newSessionId,
                ipAddress,
                userAgent
            });

            setSessionId(newSessionId);
            setLoginTime(new Date());

            // Store session info in localStorage with doctor-specific keys
            localStorage.setItem(`doctorSessionId_${doctorData.accessID}`, newSessionId);
            localStorage.setItem(`doctorLoginTime_${doctorData.accessID}`, new Date().toISOString());

        } catch (error) {
            console.error('Error tracking doctor login:', error);
        }
    };

    // Function to track doctor logout
    const trackLogout = async (doctorData?: Dr) => {
        try {
            // Only track sessions for doctors, not admins
            if (!doctorData || doctorData.role !== 'doctor') {
                return;
            }

            if (sessionId && loginTime) {
                const logoutTime = new Date();
                const sessionDuration = Math.floor((logoutTime.getTime() - loginTime.getTime()) / 1000);
                const ipAddress = await getUserIP();
                const userAgent = getUserAgent();

                await ApiClient.trackDoctorLogout({
                    doctorId: doctorData.accessID,
                    sessionId,
                    sessionDuration,
                    ipAddress,
                    userAgent
                });
            }

            // Clear session tracking data for this specific doctor
            setSessionId(undefined);
            setLoginTime(undefined);
            localStorage.removeItem(`doctorSessionId_${doctorData.accessID}`);
            localStorage.removeItem(`doctorLoginTime_${doctorData.accessID}`);
            localStorage.removeItem(`doctorConsultationDuration_${doctorData.accessID}`);

        } catch (error) {
            console.error('Error tracking doctor logout:', error);
        }
    };

    // Add beforeunload event to track logout on page close/refresh
    // TODO: Commented out for now - may cause issues in multi-doctor environment
    /*
    useEffect(() => {
        if (!doctor || !sessionId) return;

        const handleBeforeUnload = () => {
            // Use sendBeacon for reliable tracking on page unload
            if (navigator.sendBeacon && loginTime) {
                try {
                    const logoutTime = new Date();
                    const sessionDuration = Math.floor((logoutTime.getTime() - loginTime.getTime()) / 1000);

                    const data = new FormData();
                    data.append('doctorId', doctor.accessID);
                    data.append('sessionId', sessionId);
                    data.append('sessionDuration', sessionDuration.toString());

                    navigator.sendBeacon('/api/doc/v1.0/doctor/logout', data);
                } catch (error) {
                    console.error('Error tracking logout on page unload:', error);
                }
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [doctor, sessionId, loginTime]);
    */

    useEffect(() => {
        const initUser = async () => {
            try {
                const UserSession = await SupabaseClient.getSession();
                if (UserSession) {
                    setSession(UserSession);
                    setUser(UserSession.user);
                    localStorage.setItem('xdr', UserSession.user.id || "")

                    // Only fetch doctor data if we have a valid session
                    try {
                        const doctorResult = await ApiClient.getDoctorBySupabaseId(UserSession.user.id)
                        setDoctor(doctorResult)

                        // Restore session tracking if it exists for this specific doctor (only for doctors, not admins)
                        if (doctorResult.role === 'doctor') {
                            const storedSessionId = localStorage.getItem(`doctorSessionId_${doctorResult.accessID}`);
                            const storedLoginTime = localStorage.getItem(`doctorLoginTime_${doctorResult.accessID}`);
                            if (storedSessionId && storedLoginTime) {
                                setSessionId(storedSessionId);
                                setLoginTime(new Date(storedLoginTime));
                            }
                        }
                        // Check if doctor has active shift and hasn't been redirected yet
                        if (doctorResult?.accessID) {
                            try {
                                const timer = await ApiClient.fetchTimer(doctorResult.accessID);
                                const hasInitiallyRedirected = localStorage.getItem('initialRedirectDone');
                                // Only redirect on initial page load (not when starting timer from other pages)
                                if (timer?.timerKey && !hasInitiallyRedirected && window.location.pathname === '/') {
                                    localStorage.setItem('initialRedirectDone', 'true');
                                    navigate({ to: '/online-patients' });
                                }
                            } catch (timerError) {
                                console.error('Error checking timer status:', timerError);
                            }
                        }
                    }
                    catch (error) {
                        setError(error as AxiosError<unknown, any>);
                        // navigate({ to: `${location.current.pathname}${location.current.search.token ? `?token=${location.current.search.token}` : ''}` || '/doc' });

                    }
                }else{
                     setSession(undefined);
                     setUser(undefined);
                     setDoctor(undefined);
                    navigate({ to: '/login' });
                }
            } catch (error) {
                console.error('Error during session check:', error);
            } finally {
                setIsLoading(false);
            }
        };

        const authStateChange = () => {
            const result = SupabaseClient.onAuthStateChange();
            setSession(result.session);
            return result;
        };

        initUser();
        return () => {
            authStateChange().data.subscription.unsubscribe();
        };
    }, [navigate]);

//}, [navigate, location.current.pathname]);

    const login = async (email?: string, password?: string) => {
        try {
            if (email && password) {
                const result = await SupabaseClient.signInWithPassword(email, password);
                if (result) {
                    setSession(result.session);
                    setUser(result.user);
                    try {
                        const doctorResult = await ApiClient.getDoctorBySupabaseId(result.user.id);
                        setDoctor(doctorResult);

                        // Track doctor login
                        await trackLogin(doctorResult);

                        // Store doctor consultation duration in localStorage
                        if (doctorResult?.consultationDurationMinutes) {
                            localStorage.setItem(`doctorConsultationDuration_${doctorResult.accessID}`, doctorResult.consultationDurationMinutes.toString());
                        } else {
                            // Default to 6 minutes if not set
                            localStorage.setItem(`doctorConsultationDuration_${doctorResult.accessID}`, '6');
                        }

                        if (doctorResult?.accessID) {
                            try {
                                const timer = await ApiClient.fetchTimer(doctorResult.accessID);
                                if (timer?.timerKey) {
                                    localStorage.setItem('initialRedirectDone', 'true');
                                    navigate({ to: '/online-patients' });
                                    return;
                                }
                            } catch (timerError) {
                                console.error('Error checking timer status:', timerError);
                            }
                        }

                        // Navigate based on role
                        if (doctorResult?.role === 'doctor') {
                            navigate({ to: '/online-patients' });
                        } else {
                            navigate({ to: '/home' });
                        }
                    } catch (doctorError) {
                        console.error('Error fetching doctor data:', doctorError);
                        setError(doctorError as AxiosError<unknown, AppUser>);
                    }
                }
            }
        } catch (error) {
            setError(error as AxiosError<unknown, AppUser>);
            console.log({ error })
        }
    };

    const logout = async () => {
        // Track doctor logout before clearing data
        await trackLogout(doctor);

        // if (user?.id) {
        //     await ApiClient.deleteTimer(user.id)
        // }

        localStorage.removeItem('ramadanMessage')
        localStorage.removeItem('initialRedirectDone') // Clear the redirect flag on logout
        localStorage.removeItem('hasSeenInfoPopup') // Clear the GP letter popup flag on logout

        // Clear doctor-specific consultation duration
        if (doctor?.accessID) {
            localStorage.removeItem(`doctorConsultationDuration_${doctor.accessID}`);
        }

        const result = await SupabaseClient.signOut();
        if (result.error) {
            setError(result.error);
            return;
        }

        setUser(undefined);
        setSession(undefined);
        setDoctor(undefined);
        navigate({ to: '/login' });
    };

    return (
        <AuthContext.Provider value={{ user, session, login, logout, error, setError, doctor, patients, setPatient }}>
            {isLoading ? <LoadingScreen /> : (
                <>
                    {error && <Grid sx={{ mt: 10 }}>
                        <ApiErrorMessage error={error} />
                    </Grid>}
                    {children}
                </>
            )}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = React.useContext(AuthContext);
    if (!context) {
        throw new Error(`useAuth must be used within an AuthProvider`);
    }
    return context;
};
