import { useState, useEffect, useCallback } from 'react';
import { ApiClient } from '../services';

interface RequestCounts {
  all: number;
  requests: number;
  unread: number;
}

export const useHasRequests = () => {
  const [hasRequests, setHasRequests] = useState<boolean>(true); // Start with true to show initially
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const checkForRequests = useCallback(async () => {
    try {
      setLoading(true);
      // First check for pending/submitted requests (faster check)
      const pendingResponse = await ApiClient.getDoctorPendingRequests(false);
      const pendingCounts: RequestCounts = pendingResponse.data.counts || { all: 0, requests: 0, unread: 0 };

      // If we have pending requests, show immediately
      if (pendingCounts.requests > 0) {
        setHasRequests(true);
        setError(null);
        setLoading(false);
        return;
      }

      // If no pending requests, check for all requests (including past)
      const allResponse = await ApiClient.getDoctorPendingRequests(true);
      const allCounts: RequestCounts = allResponse.data.counts || { all: 0, requests: 0, unread: 0 };

      // Show RequestsWindow if there are any requests at all (pending or past)
      setHasRequests(allCounts.all > 0);
      setError(null);
    } catch (err) {
      console.error('Error checking for requests:', err);
      setError('Failed to check requests');
      // Default to showing requests window on error to be safe
      setHasRequests(true);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkForRequests();

    // Set up polling to check for new requests every 30 seconds
    const interval = setInterval(checkForRequests, 30000);

    return () => clearInterval(interval);
  }, [checkForRequests]);

  return { hasRequests, loading, error, refetch: checkForRequests };
};
