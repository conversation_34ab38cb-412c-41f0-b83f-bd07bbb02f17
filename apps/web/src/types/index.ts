import { User } from "@supabase/supabase-js";

export interface Patient {
  id: string;
  name: string;
}

export interface AppUser extends User {
  email?: string;
  name?: string;
  password?: string;
}

export interface loginData {
  email?: string;
  password?: string;
}

export type PatientInfo = {
  fullName: string;
  email?: string;
  phone?: string;
};

export type Admission = {
  id: string;
  patientID: string;
  createdAt: string;
  admitted: boolean;
};

// TODO: Build a function that will put in one object 22 and 29 of the same consultation.

export type TreatmentPlanHistory = {
  id: string;
  fullName: string;
  patientID: string;
  consultationId: string;
  createdAt: string;
  updatedAt: string;
  drName: string;
  drId?: string;
  outcome: string;
  treatmentPlan?: {
    dispensingInterval?: string | null;
    numberOfRepeats?: string | null;
    supplyDate?: string | null;
    supplyExpiration?: string | null;
    doctorNotes?: string | null;
    treatments?: {
      t22?: {
        dailyDose: string | null;
        maxDailyDose: string | null;
        totalQuantity: string | null;
        pouchCount: string | null;
        strength: string;
      } | null;
      t29?: {
        dailyDose: string | null;
        maxDailyDose: string | null;
        totalQuantity: string | null;
        pouchCount: string | null;
        strength: string;
      } | null;
    };
    strainAdvice?: {
      [key: string]: string;
    };
    sideEffects?: {
      effect1: string | null;
      effect2: string | null;
      effect3: string | null;
    };
    previousTreatments?: string[];
  };
  date: string;
  dosePerDay22: string;
  maxDosePerDay22: string;
  strengthAndConcentration22: string;
  totalQuantity22: string;
  numberOfRepeat22: string;
  supplyInterval22: string;
  dosePerDay29: string;
  maxDosePerDay29: string;
  strengthAndConcentration29: string;
  totalQuantity29: string;
  numberOfRepeat29: string;
  supplyInterval29: string;
  drNotes: string;
  mentalHealthSupprtingDocument: string;
  type: string;
  totalRows: string;
};

export type OrderHistory = {
  id: string;
  fullName: string;
  orderNumber: string;
  patientID: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  quantity: string;
  strength: string;
  remainingQuantity: string;
  initialQuantity: string;
  remainingRepeat: string;
  initialRepeat: string;
  type: string;
  totalRows: string;
};

export type OrderItem = {
  sku: string;
  trade_name: string;
  quantity: number;
  strength: string;
};

export type PatientOrder = {
  order_id: string;
  wp_user_id: string;
  zoho_contact_id: string;
  allowanceLeft: number;
  allowanceLeft22: number;
  allowanceLeft29: number;
  email: string;
  treatmentPlanDate: string;
  drName: string;
  createdAt: string;
  updatedAt: string;
  type: string;
  collector: string;
  orderDateAndTime: string;
  repeatLeft: number;
  repeatLeft22: number;
  repeatLeft29: number;
  id: string;
  items: OrderItem[];
};

export type QuestionnaireShape = {
  id: string;
  fullName?: string;
  form: QuestionAnswer[];
  patientID?: string;
  createdAt: string;
  updatedAt: string;
  type: string;
  totalRows?: string;
  data: Questionnaire[];
};
// TODO: Reshape questionnaire to this shape when coming from DB
export type QuestionAnswer = {
  id: string;
  question: string;
  answers: string;
  createdAt: string;
  updatedAt: string;
};
export type Questionnaire = {
  id: string;
  // fullName?: string;
  form: QuestionAnswer[];
  // patientID?: string;
  question: string;
  answers: string;
  createdAt: string;
  updatedAt: string;
  type: string;
  totalRows?: string;
};

export type HealthCheck = {
  id: string;
  question: string;
  answers: string;
  createdAt: string;
  updatedAt: string;
  type: string;
  healthCheckID: string;
  email: string;
};

export type HealthCheckShape = {
  id: string;
  form: HealthCheck[];
  createdAt: string;
  updatedAt: string;
  type: string;
  totalRows?: string;
};
export type PatientHistory = {
  treatmentPlan?: TreatmentPlanHistory[];
  healthCheck?: Questionnaire[];
  questionnaire?: Questionnaire[];
  orders?: OrderHistory[];
};

export type PatientConsultation = {
  id?: string;
  patientID?: string;
  joinedAt?: number;
  consultationDate?: string;
  consultationDateAndTime?: string;
  consultationDuration?: number;
  consultationURL?: string;
  meetingOngoing?: boolean;
  drJoined?: boolean;
  consultationStart?: string;
  completed?: boolean;
  consultationEnd?: string;
  notificationSent?: boolean;
  notificationSentDateTime?: string;
  drId?: string;
};

export type PatientData = {
  firstName?: string;
  lastName?: string;
  fullName?: string;
  email?: string;
  phone?: string;
  state?: string;
  dob?: string;
  usedCannabisBefore?: boolean;
  mobile?: string;
  consultation?: PatientConsultation;
  returningPatient?: boolean;
  patientID: string;
  locked?: boolean;
  drLocked?: string;
  assignedDoctorID?: string | null; // Added for new queue management system
  zohoID?: string;
  drDecision?: string;
  drNotes?: string;
  history?: PatientHistory;
  userJourney?: "waitingRoom" | "landedWebsite" | "joined";
  attempt?: number;
  noShow?: boolean;
};

export type PatientsFullHistory = {
  fullName?: string;
  email?: string;
  phone?: string;
  consultations?: PatientConsultation[];
  returningPatient?: boolean;
  patientID: string;
  locked?: boolean;
  drLocked?: string;
  zohoID?: string;
  state?: string;
  history?: PatientHistory;
};

export type TreatmentPlan = {
  strengthAndConcentration?: string;
  dosePerDay?: string;
  maxDosePerDay?: string;
  totalQuantity?: string;
  numberOfRepeat?: string;
  supplyInterval?: string;
};

export type ConsultationOutCome =
  | "Approve Unrestricted"
  | "Approve 29% Subject To 22% Trial"
  | "Approve Subject To Discharge Form"
  | "Approve 22% Subject To CBD Trial"
  | "Approve Subject To GP Referral"
  | "No Show"
  | "Reject";

export type PatientTreatmentPlan = {
  patient?: PatientData;
  email?: {
    introMessage?: { intro: string; conclusion: string };
    listItemText?: ListItemTextTpProps;
    listTitle?: ListTitleTpProps;
    otherTreatment?: OtherTreatmentTpProps;
    checkedSativa?: string[];
    checkedIndica?: string[];
    checkedHybrid?: string[];
  };
  treatmentPlan?: {
    dispensingInterval?: string | null;
    numberOfRepeats?: string | null;
    supplyDate?: string | null;
    supplyExpiration?: string | null;
    doctorNotes?: string | null;
    treatments?: {
      t22?: {
        dailyDose: string | null;
        maxDailyDose: string | null;
        totalQuantity: string | null;
        pouchCount: string | null;
        strength: string;
      } | null;
      t29?: {
        dailyDose: string | null;
        maxDailyDose: string | null;
        totalQuantity: string | null;
        pouchCount: string | null;
        strength: string;
      } | null;
    };
    strainAdvice?: {
      [key: string]: string;
    };
    sideEffects?: {
      effect1: string | null;
      effect2: string | null;
      effect3: string | null;
    };
    previousTreatments?: string[];
  };
  outcome?: ConsultationOutCome;
  drNotes?: string;
  diagnosis?: string;
  mentalHealthSupportingDocumentation?: "Yes" | "No";
  date?: string;
  drId?: string;
  drAphraNumber?: string;
  drName?: string;
  "22"?: TreatmentPlan;
  "29"?: TreatmentPlan;
};

export type Timer = {
  id: string;
  timerId: number;
  reassignmentTimerId?: number;
  drID: string;
};

export type TimeStarter = {
  joinedPatient: PatientQueue[];
  onlinePatient: PatientQueue[];
  timerKey: string;
};

export type DoctorQueue = {
  id: string;
  patientID: string;
  doctorID: string;
  assignedAt: string;
  status: "ASSIGNED" | "CONSULTED" | "REASSIGNED";
  createdAt: string;
  updatedAt: string;
};

export type Dr = {
  id?: string;
  email?: string;
  name?: string;
  username?: string;
  accessID: string;
  status?: string;
  role?: "doctor" | "admin" | "superAdmin";
  password?: string;
  confirmPassword?: string;
  aphraNumber?: string;
  consultationDurationMinutes?: number;
};

export type AvailabilityEntry = {
  day?: string;
  date?: string;
  start?: string;
  end?: string;
  availability?: number;
  id?: string;
  status?: "active" | "pending" | "editing" | "deleted";
  doctorID?: string; // UUID string
  doctorName?: string;
};

// Interface for source slot data in aggregated slots
export interface SourceSlot {
  range_id: string;
  slot_id: string;
  doctorID: string;
  remaining: number;
}

export type Slot = {
  slot: string;
  remaining: number;
  id: string;
  range_id: string;
  bookedByAdminId?: string;
  bookingType?: 'patient' | 'admin';
  doctorID?: string; // For admin bookings
  source_slots?: SourceSlot[]; // For aggregated patient bookings
  noShowRemaining?: number; // For no-show availability tracking
};

export type AvailableDate = {
  day?: string;
  date?: string;
  start?: string;
  end?: string;
  interval?: number;
  availability?: number;
  range_id?: string;
  status?: "active" | "pending" | "editing" | "deleted";
  slots?: Slot[];
  doctorID?: string; // UUID string
  doctorName?: string;
};

export type PatientQueue = {
  status:
    | "ONLINE"
    | "OFFLINE"
    | "JOINED"
    | "COMPLETED"
    | "AWAY"
    | "ADMITTED"
    | "CONFIRMED"
    | "ENDED"
    | "NO-SHOW";
  patientID: string;
  fullName: string;
  createdAt: string | null;
  updatedAt: string | null;
  joinedAt: string | null;
  notificationSent: boolean | null;
  notificationSentDateTime: string | null;
  leftAt: string | null;
  completedAt: string | null;
  joinedCallAt: string | null;
  admittedAt: string | null;
  callEndedAt: string | null;
  confirmedAt: string | null;
  firstTimeJoined: string | null;
  attempt?: number;
  noShow?: boolean;
  tech_issue?: boolean;
  allowed?: boolean;
  assignedDoctorID?: string | null;
  consultedDoctorID?: string | null;
  consultationDate?: string | null;
  consultationId?: string | null; // Added to link directly to the consultation
  consultationCompleted?: boolean; // Added to track if the underlying consultation is completed
};

export type PatientQueueResponse = {
  patients: PatientQueue[];
  totalPatientsForDay: number;
};

export type ConsultationFilter = {
  id: string;
  patientID: string;
  drId: string | null;
  email: string;
  joinedAt: string | null;
  consultationDate: string;
  meetingOngoing: boolean;
  drJoined: boolean;
  consultationStart: string;
  consultationEnd: string;
  notificationSent: boolean;
  notificationSentDateTime: string | null;
  completed: boolean;
  createdAt: string;
  updatedAt: string;
  fullName: string;
  password: string | null;
  returningPatient: boolean;
  zohoID: string;
  locked: boolean;
  drLocked: string | null;
  state: string;
  lastCompletedForm: string | null;
};

export type StepsType = "step1" | "step2" | "step3" | "step4" | "";

export type ListTitleTpProps = {
  title1: string;
  title2: string;
  title3: string;
};

export type ListItemTextTpProps = {
  item1: string;
  item2: string;
  item3: string;
};

export type OtherTreatmentTpProps = {
  [key: string]: string;
};

export type ListItemsAdvice = {
  [key: string]: string;
};

export type ListValuesStrainAdvice = {
  option1: string;
  option2: string;
  option3: string;
  option4: string;
  option5: string;
  option6: string;
};

export type TimeLeft = {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number; // Total time in milliseconds
};

export interface FormValue {
  dosePerDay?: string;
  maxDosePerDay?: string;
  totalQuantity?: string;
  numberOfRepeat?: string;
  supplyInterval?: string;
}
export type DrActivity = {
  name?: string;
  email?: string;
  createdAt?: string;

  action?: string;
  id?: string;
};
export interface TreatmentPlanWithPatient {
  [key: string]:
    | FormValue
    | undefined
    | ConsultationOutCome
    | string
    | PatientData;
  22?: FormValue;
  29?: FormValue;
  outcome?: ConsultationOutCome;
  patient?: PatientData;
  zohoID?: string;
  date?: string;
  mentalHealthSupportingDocumentation?: string;
  idVerified?: string;
}

export interface FormValue {
  dosePerDay?: string;
  maxDosePerDay?: string;
  totalQuantity?: string;
  numberOfRepeat?: string;
  supplyInterval?: string;
}

export type ConsentFormData = {
  voluntary_consent: boolean;
  legally_competent: boolean;
  sufficient_information: boolean;
  understanding_risks: boolean;
  medical_cannabis_unapproved: boolean;
  illegal_prescription: boolean;
  drug_interactions: boolean;
  no_use_while_treated: boolean;
  illegal_to_minors: boolean;
  signature: string;
  signatureEvidence?: {
    ipAddress: string;
    deviceInfo: string;
  };
};

export type ConsentFormResponse = {
  success: boolean;
  message: string;
  data: {
    consentId: string;
  };
};

export type ConsentFormStatusResponse = {
  success: boolean;
  data: {
    consentFormCompleted: boolean;
  };
};

export type DoctorSession = {
  id: string;
  doctorId: string;
  sessionId?: string;
  action: "LOGIN" | "LOGOUT";
  timestamp: string;
  sessionDuration?: number; // in seconds, only for logout events
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
};

export type DoctorLoginRequest = {
  doctorId: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
};

export type DoctorLogoutRequest = {
  doctorId: string;
  sessionId?: string;
  sessionDuration?: number;
  ipAddress?: string;
  userAgent?: string;
};

export type DoctorStatusUpdate = {
  doctorId: string;
  doctorName: string;
  action: "LOGIN" | "LOGOUT";
  timestamp: string;
  sessionId?: string;
  sessionDuration?: number;
};

export type Product = {
  [productName: string]: {
    requests: {
      drName: string;
      budQuantity: number;
    }[];
    totalBudQuantity: number;
    tradeName: string;
    strength: string;
    balance: number;
  };
};

export type OutputDoc = {
  [doctorName: string]: {
    products: {
      [productName: string]: {
        baseName: string;
        totalQuantity: number;
        totalWeight: number;
        totalBudQuantity: number;
        tradeName: string;
      };
    };
  };
};

export type OrderPatient = {
  first_name: string
  last_name: string
  user_email: string
  zoho_contact_id: string
  order_number: string
  consulting_doctor: string
  order_date_modified: string
}
export type ReportData = {
	lead_id: string;
	patientID: string;
	createdAt: string;
	pregnancy: string;
	condition: string;
	cardiovascular_diseases: string;
	psychotic_disorder: string;
	tried_medications: string[];
	response: string;
	explanation: string;
	risk_score: number;
	patientName?: string;
};