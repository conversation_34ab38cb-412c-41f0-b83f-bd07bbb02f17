import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>, Accordion, AccordionSummary, AccordionDetails, LinearProgress, Box } from "@mui/material";
import { Fragment, useEffect, useRef, useState, useMemo } from "react";
import Grid from "@mui/material/Grid2";
import { PatientData, PatientQueue } from "../../types";
import { usePatient } from "../../hooks/patient-provider";
import { useMeeting } from "../../hooks/meeting-provider";
import { useAuth } from "../../hooks/auth-provider";
import { formatTimeWithMeridian } from "../../utils";
import { ApiClient } from "../../services";
import { AxiosError } from "axios";
import LoadingScreen from "../../utils/loading-screen";
import CancelIcon from '@mui/icons-material/Cancel';
import StackedBarChartIcon from '@mui/icons-material/StackedBarChart';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
//import RefreshIcon from '@mui/icons-material/Refresh';
import ExceededAttemptsSection from './exceeded-attempts-section';
import DoctorStatusIndicator from './doctor-status-indicator';
//import { useSnackbar } from 'notistack';

type MessageLogs = {
    item: string;
    time: string | null;
    message: string;
}
const PatientQueueView = () => {
    const { patientQueue, setPatientQueue } = useMeeting()
    const {
        patients,
        setError,
        timerKey,
        setTimerKey,
        drList,
        setDrList,
        allDoctors
    } = usePatient()
    const [isLoading, setIsLoading] = useState(true)
    const [patientSelected, setPatientSelected] = useState<PatientQueue | undefined>(undefined)
    const [nextPatient, setNextPatient] = useState<PatientData>()
    const [detailsLogs, setDetailsLogs] = useState<MessageLogs[]>([])
    const [restartTimerDialog, setRestartTimerDialog] = useState(false)
    const [exceededAttemptsPatients, setExceededAttemptsPatients] = useState<PatientQueue[]>([])
    const [openReportDialog, setOpenReportDialog] = useState(false)
   ///const [resettingAttempts, setResettingAttempts] = useState<Record<string, boolean>>({})
    const [totalPatientsForDay, setTotalPatientsForDay] = useState<number>(0)
    const timerValue = useRef<string>('')

    const { doctor } = useAuth()
    //const { enqueueSnackbar } = useSnackbar()

    // Function to update drList with doctors from patientQueue
    const updateDrListWithQueueDoctors = () => {
        if (!patientQueue || !allDoctors) return;

        // Get all assigned doctor IDs from the queue
        const assignedDoctorIds = patientQueue
            .filter(patient => patient.assignedDoctorID)
            .map(patient => patient.assignedDoctorID as string);

        if (assignedDoctorIds.length === 0) return;

        // Create a map of doctor IDs to doctor objects
        const doctorMap = { ...drList };

        for (const doctorId of assignedDoctorIds) {
            // Skip if we already have this doctor
            if (doctorMap[doctorId]) continue;

            // Try to find the doctor in allDoctors
            const doctor = allDoctors.find(d => d.accessID === doctorId);
            if (doctor) {
                doctorMap[doctorId] = doctor;
            }
        }

        // Update drList
        setDrList(doctorMap);
    };

    // Helper to format doctor display
    const formatDoctorDisplay = (doctorId?: string | null) => {
        if (!doctorId) return 'Unassigned';
        return drList[doctorId]?.username || `${doctorId.substring(0, 8)}...`;
    };

    // Group patients by assigned doctor
    const groupedPatientQueues = useMemo(() => {
        if (!patientQueue || patientQueue.length === 0) {
            return {};
        }

        const groups: { [doctorId: string]: PatientQueue[] } = {};

        patientQueue.forEach(patient => {
            const doctorId = patient.assignedDoctorID || 'unassigned';
            if (!groups[doctorId]) {
                groups[doctorId] = [];
            }
            groups[doctorId].push(patient);
        });

        return groups;
    }, [patientQueue]);

    // Get sorted doctor IDs for consistent display order
    const sortedDoctorIds = useMemo(() => {
        const doctorIds = Object.keys(groupedPatientQueues);
        return doctorIds.sort((a, b) => {
            // Put unassigned patients at the end
            if (a === 'unassigned') return 1;
            if (b === 'unassigned') return -1;

            // Sort by doctor name
            const nameA = formatDoctorDisplay(a);
            const nameB = formatDoctorDisplay(b);
            return nameA.localeCompare(nameB);
        });
    }, [groupedPatientQueues, drList]);

    // Get queue statistics for a doctor
    const getQueueStats = (patients: PatientQueue[]) => {
        const online = patients.filter(p => p.status === 'ONLINE').length;
        const joined = patients.filter(p => p.status === 'JOINED').length;
        const completed = patients.filter(p => p.status === 'COMPLETED').length;
        const noShow = patients.filter(p => p.status === 'NO-SHOW').length;
        const notified = patients.filter(p => p.notificationSent === true).length;
        const total = patients.length;

        return { online, joined, completed, noShow, notified, total };
    };

    // Get overall notification statistics
    const getOverallNotificationStats = useMemo(() => {
        // Use the backend-provided total count for all patients scheduled today
        const totalPatients = totalPatientsForDay;
        const notifiedPatients = patientQueue ? patientQueue.filter(p => p.notificationSent === true).length : 0;
        const notificationRatio = `${notifiedPatients}/${totalPatients}`;

        return { totalPatients, notifiedPatients, notificationRatio };
    }, [patientQueue, totalPatientsForDay]);

    // Get notification statistics for a specific doctor
    const getDoctorNotificationStats = (patients: PatientQueue[]) => {
        const total = patients.length;
        const notified = patients.filter(p => p.notificationSent === true).length;
        return { notified, total };
    };

    // Component to render individual patient queue item
    const PatientQueueItem = ({ patient }: { patient: PatientQueue }) => {
        const timeline = [
            {
                item: 'notificationSentDateTime',
                time: patient.notificationSentDateTime,
                message: `Notification sent at ${formatTimeWithMeridian(patient.notificationSentDateTime)}`
            },
            {
                item: 'joinedAt',
                time: patient.joinedAt,
                message: `Joined Queue at ${formatTimeWithMeridian(patient.joinedAt)}`
            },
            {
                item: 'admittedAt',
                time: patient.admittedAt,
                message: `Admitted at ${formatTimeWithMeridian(patient.admittedAt)}`
            },
            {
                item: 'joinedCallAt',
                time: patient.joinedCallAt,
                message: `Joined Call at ${formatTimeWithMeridian(patient.joinedCallAt)}`
            },
            {
                item: 'completedAt',
                time: patient.completedAt,
                message: `Dr Completed Consultation at ${formatTimeWithMeridian(patient.completedAt)}`
            },
            {
                item: 'leftAt',
                time: patient.leftAt,
                message: `Left Queue at ${formatTimeWithMeridian(patient.leftAt)}`
            },
            {
                item: 'confirmedAt',
                time: patient.confirmedAt,
                message: `TP Confirmed at ${formatTimeWithMeridian(patient.confirmedAt)}`
            },
            {
                item: 'callEndedAt',
                time: patient.callEndedAt,
                message: `Call Ended At ${formatTimeWithMeridian(patient.callEndedAt)}`
            },
            {
                item: 'tech_issue',
                time: patient.tech_issue ? patient.callEndedAt : null,
                message: patient.tech_issue ? 'Patient marked with technical issues' : ''
            }
        ];

        const sortedTimeline = timeline
            .filter(entry => entry.time !== null && entry.time !== undefined)
            .sort((a, b) => {
                const timeA = new Date(a.time as string | number | Date).getTime();
                const timeB = new Date(b.time as string | number | Date).getTime();
                return timeA - timeB;
            });

        return (
            <Fragment key={`${patient.patientID}`}>
                <Grid sx={{ width: '100%', maxWidth: "100%" }}>
                    <Grid
                        container
                        justifyContent={'start'}
                        sx={{
                            boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                            border: '1px solid rgba(0, 0, 0, 0.1)',
                            p: 2,
                            mb: 1,
                            mr: 1
                        }}
                        alignItems={'center'}>
                        <Grid sx={{ flexGrow: 1 }} container direction={'column'}>
                            <Grid container spacing={1} alignItems={'start'}>
                                <Grid container direction="column" sx={{ flexGrow: 1 }}>
                                    <Typography sx={{ mb: 0.5, fontWeight: 'bold' }}>
                                        {patient.fullName}
                                    </Typography>
                                    {patient.consultationDate && (
                                        <Typography sx={{ fontSize: '10px', color: '#666', mb: 1 }}>
                                            Consultation: {formatTimeWithMeridian(patient.consultationDate)}
                                        </Typography>
                                    )}
                                </Grid>
                                <Grid>
                                    <Button variant='text' sx={{ textTransform: 'none', fontSize: '10px', width: '40px', height: '20px' }} onClick={async () => {
                                        handleShowDetails(patient)
                                    }}>
                                        Logs
                                    </Button>
                                </Grid>
                            </Grid>

                            {/* Doctor assignment and notification status */}
                            <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                <Typography sx={{ fontSize: '10px', fontWeight: 'bold', color: patient.assignedDoctorID ? 'green' : 'grey' }}>
                                    Assigned to: {formatDoctorDisplay(patient.assignedDoctorID)}
                                </Typography>
                                <Typography sx={{
                                    fontSize: '9px',
                                    fontWeight: 'bold',
                                    color: patient.notificationSent ? 'green' : '#ff9800',
                                    backgroundColor: patient.notificationSent ? '#e8f5e8' : '#fff3e0',
                                    px: 1,
                                    py: 0.25,
                                    borderRadius: '8px',
                                    ml: 1
                                }}>
                                    {patient.notificationSent ? '✓ Notified' : '⏳ Pending'}
                                </Typography>
                            </Grid>

                            {/* Attempts display and reset button */}
                            <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                <Typography sx={{
                                    fontSize: '9px',
                                    fontWeight: 'bold',
                                    color: (patient.attempt || 0) > 0 ? '#ff5722' : '#666',
                                    backgroundColor: (patient.attempt || 0) > 0 ? '#ffebee' : '#f5f5f5',
                                    px: 1,
                                    py: 0.25,
                                    borderRadius: '8px'
                                }}>
                                    Attempts: {patient.attempt || 0}
                                </Typography>
                                {/* {(patient.attempt || 0) > 0 && (
                                    <Button
                                        variant="contained"
                                        size="small"
                                        startIcon={<RefreshIcon />}
                                        disabled={resettingAttempts[patient.patientID]}
                                        onClick={() => handleResetPatientAttempts(patient)}
                                        sx={{
                                            textTransform: 'none',
                                            backgroundColor: '#2196f3',
                                            fontSize: '8px',
                                            height: '20px',
                                            minWidth: '60px',
                                            ml: 1,
                                            '&:hover': { backgroundColor: '#1976d2' }
                                        }}
                                    >
                                        {resettingAttempts[patient.patientID] ? 'Resetting...' : 'Reset'}
                                    </Button>
                                )} */}
                            </Grid>

                            {sortedTimeline.map((timelineItem, index) => {
                                return (
                                    <Typography sx={{ fontSize: '10px' }} key={`${index}-${timelineItem.time}`}>
                                        {timelineItem.message}
                                    </Typography>
                                )
                            })}
                        </Grid>
                        <Grid>
                            <Typography
                                sx={{
                                    fontSize: '10px',
                                    fontWeight: 'bold',
                                    color:
                                        patient.status?.toUpperCase() === 'ONLINE' ? 'green' :
                                            patient.status?.toUpperCase() === 'JOINED' ? 'blue' :
                                                patient.status?.toUpperCase() === 'COMPLETED' ? 'green' :
                                                    patient.status?.toUpperCase() === 'AWAY' ? 'red' :
                                                        'black'

                                }}>
                                {patient.status?.toUpperCase()}
                                {patient.tech_issue && <span style={{ marginLeft: '4px', color: 'orange' }}>(TI)</span>}
                            </Typography>
                        </Grid>
                        {patient.status?.toUpperCase() === 'ONLINE' &&
                            <>
                                <Grid>
                                    <Button sx={{ fontSize: '10px', backgroundColor: 'green', ml: 1 }} variant='contained' onClick={async () => {
                                        await ApiClient.postPatientOffline(patient.patientID)

                                        await ApiClient.updateMeetingStatus(patient.patientID, false)
                                            .catch((error) => {
                                                setError(error as AxiosError<unknown, any>)
                                            })

                                    }}>
                                        TURN OFFLINE
                                    </Button>
                                </Grid>
                                <Grid>
                                    <Button sx={{ fontSize: '10px', backgroundColor: 'green', ml: 1 }} disabled={patient.notificationSent || false} variant='contained' onClick={async () => {
                                        await ApiClient.sendPatientToDoctor(patient.patientID)
                                            .catch((error) => {
                                                setError(error as AxiosError<unknown, any>)
                                            })

                                        await ApiClient.updateMeetingStatus(patient.patientID, true)
                                            .catch((error) => {
                                                setError(error as AxiosError<unknown, any>)
                                            })


                                    }}>
                                        NOTIFY
                                    </Button>
                                </Grid>
                            </>
                        }
                    </Grid>
                </Grid>
                <Collapse in={patient.patientID === patientSelected?.patientID} timeout={300}>
                    <Grid sx={{ width: '100%', maxWidth: "100%" }}>
                        <Grid
                            container
                            justifyContent={'start'}
                            sx={{
                                boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                border: '1px solid rgba(0, 0, 0, 0.1)',
                                p: 2,
                                mb: 1,
                                mr: 1
                            }}
                            alignItems={'center'}>
                            <Grid sx={{ flexGrow: 1 }} container direction={'column'}>
                                <Grid container spacing={1} alignItems={'start'}>
                                    <Grid container direction="column">
                                        <Typography sx={{ mb: 0.5, fontSize: '10px', fontWeight: 'bold' }}>
                                            {patient.fullName} Detailed logs
                                        </Typography>
                                        {patient.consultationDate && (
                                            <Typography sx={{ fontSize: '9px', color: '#666', mb: 1 }}>
                                                Consultation: {formatTimeWithMeridian(patient.consultationDate)}
                                            </Typography>
                                        )}
                                    </Grid>
                                </Grid>

                                {/* Doctor assignment and notification status in details */}
                                <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                    <Typography sx={{ fontSize: '10px', fontWeight: 'bold', color: patient.assignedDoctorID ? 'green' : 'grey' }}>
                                        Assigned to: {formatDoctorDisplay(patient.assignedDoctorID)}
                                    </Typography>
                                    <Typography sx={{
                                        fontSize: '9px',
                                        fontWeight: 'bold',
                                        color: patient.notificationSent ? 'green' : '#ff9800',
                                        backgroundColor: patient.notificationSent ? '#e8f5e8' : '#fff3e0',
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: '8px',
                                        ml: 1
                                    }}>
                                        {patient.notificationSent ? '✓ Notified' : '⏳ Pending'}
                                    </Typography>
                                </Grid>

                                {/* Attempts display and reset button in details */}
                                <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                    <Typography sx={{
                                        fontSize: '9px',
                                        fontWeight: 'bold',
                                        color: (patient.attempt || 0) > 0 ? '#ff5722' : '#666',
                                        backgroundColor: (patient.attempt || 0) > 0 ? '#ffebee' : '#f5f5f5',
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: '8px'
                                    }}>
                                        Attempts: {patient.attempt || 0}
                                    </Typography>
                                    {/* {(patient.attempt || 0) > 0 && (
                                        <Button
                                            variant="contained"
                                            size="small"
                                            startIcon={<RefreshIcon />}
                                            disabled={resettingAttempts[patient.patientID]}
                                            onClick={() => handleResetPatientAttempts(patient)}
                                            sx={{
                                                textTransform: 'none',
                                                backgroundColor: '#2196f3',
                                                fontSize: '8px',
                                                height: '20px',
                                                minWidth: '60px',
                                                ml: 1,
                                                '&:hover': { backgroundColor: '#1976d2' }
                                            }}
                                        >
                                            {resettingAttempts[patient.patientID] ? 'Resetting...' : 'Reset'}
                                        </Button>
                                    )} */}
                                </Grid>

                                {detailsLogs.map((logItem, index) => {
                                    return (
                                        <Typography sx={{ fontSize: '10px' }} key={`${index}-${logItem.time}`}>
                                            {logItem.message}
                                        </Typography>
                                    )
                                })}
                            </Grid>
                            <Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Collapse>
                {
                    patient.status?.toUpperCase() === 'NO-SHOW' &&
                    <Grid sx={{ width: '100%', maxWidth: "100%" }}>
                        <Grid
                            container
                            justifyContent={'start'}
                            sx={{
                                boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                border: '1px solid rgba(0, 0, 0, 0.1)',
                                p: 2,
                                mb: 1,
                                mr: 1
                            }}
                            alignItems={'center'}>
                            <Grid sx={{ flexGrow: 1 }} container direction={'column'}>
                                <Grid container spacing={1} alignItems={'start'}>
                                    <Typography sx={{ mb: 1, color: 'red', fontWeight: 'bold', fontSize: '10px' }}>
                                        {patient.fullName?.toUpperCase()}
                                    </Typography>
                                </Grid>

                                <Typography sx={{ fontSize: '10px' }}>
                                    Mutliple No Shows
                                </Typography>

                                {/* Doctor assignment and notification status for no shows */}
                                <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                    <Typography sx={{ fontSize: '10px', fontWeight: 'bold', color: patient.assignedDoctorID ? 'green' : 'grey' }}>
                                        Assigned to: {formatDoctorDisplay(patient.assignedDoctorID)}
                                    </Typography>
                                    <Typography sx={{
                                        fontSize: '9px',
                                        fontWeight: 'bold',
                                        color: patient.notificationSent ? 'green' : '#ff9800',
                                        backgroundColor: patient.notificationSent ? '#e8f5e8' : '#fff3e0',
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: '8px',
                                        ml: 1
                                    }}>
                                        {patient.notificationSent ? '✓ Was Notified' : '⏳ Never Notified'}
                                    </Typography>
                                </Grid>

                                {/* Attempts display and reset button for no shows */}
                                <Grid container spacing={1} alignItems="center" sx={{ mb: 1 }}>
                                    <Typography sx={{
                                        fontSize: '9px',
                                        fontWeight: 'bold',
                                        color: (patient.attempt || 0) > 0 ? '#ff5722' : '#666',
                                        backgroundColor: (patient.attempt || 0) > 0 ? '#ffebee' : '#f5f5f5',
                                        px: 1,
                                        py: 0.25,
                                        borderRadius: '8px'
                                    }}>
                                        Attempts: {patient.attempt || 0}
                                    </Typography>
                                    {/* {(patient.attempt || 0) > 0 && (
                                        <Button
                                            variant="contained"
                                            size="small"
                                            startIcon={<RefreshIcon />}
                                            disabled={resettingAttempts[patient.patientID]}
                                            onClick={() => handleResetPatientAttempts(patient)}
                                            sx={{
                                                textTransform: 'none',
                                                backgroundColor: '#2196f3',
                                                fontSize: '8px',
                                                height: '20px',
                                                minWidth: '60px',
                                                ml: 1,
                                                '&:hover': { backgroundColor: '#1976d2' }
                                            }}
                                        >
                                            {resettingAttempts[patient.patientID] ? 'Resetting...' : 'Reset'}
                                        </Button>
                                    )} */}
                                </Grid>
                            </Grid>
                            <Grid>
                                <Typography
                                    sx={{
                                        fontSize: '10px',
                                        fontWeight: 'bold',
                                        color: 'red'
                                    }}>
                                    NO-SHOW
                                </Typography>
                            </Grid>
                            <Grid>
                                <Button sx={{ fontSize: '10px', backgroundColor: 'green', ml: 1 }} variant='contained' onClick={async () => {
                                    await ApiClient.postPatientWaitingQueue(patient.patientID)

                                    await ApiClient.updateMeetingStatus(patient.patientID, true)
                                        .catch((error) => {
                                            setError(error as AxiosError<unknown, any>)
                                        })
                                }}>
                                    ALLOW
                                </Button>
                            </Grid>
                        </Grid>
                    </Grid>
                }
            </Fragment>
        );
    };

    useEffect(() => {
        if (patientQueue && patientQueue.length > 0 && allDoctors && allDoctors.length > 0) {
            updateDrListWithQueueDoctors();
        }
    }, [patientQueue, allDoctors]);

    useEffect(() => {
        if (patientQueue) {
            const filteredPatient = patientQueue?.filter(p => !p.completedAt && p.status !== 'NO-SHOW')
            if (filteredPatient) {
                setNextPatient(filteredPatient[0] as unknown as PatientData)
            }
        }
    }, [patientQueue])

    const fetchExceededAttemptsPatients = async () => {
        try {
            const exceededPatients = await ApiClient.fetchPatientsWithExceededAttempts();
            setExceededAttemptsPatients(exceededPatients);
        } catch (error) {
            console.error("Failed to fetch patients with exceeded attempts:", error);
            setError(error as AxiosError<unknown, Error>);
        }
    };

    const handlePatientAttemptsReset = (updatedPatient: PatientQueue) => {
        setExceededAttemptsPatients(prev =>
            prev.filter(p => p.patientID !== updatedPatient.patientID)
        );
    };

    // const handleResetPatientAttempts = async (patient: PatientQueue) => {
    //     if (!patient.patientID) return;

    //     setResettingAttempts(prev => ({ ...prev, [patient.patientID]: true }));
    //     try {
    //         await ApiClient.resetPatientAttempts(patient.patientID);

    //         // Update the patient queue with the reset patient data
    //         setPatientQueue(prev =>
    //             prev.map(p =>
    //                 p.patientID === patient.patientID
    //                     ? { ...p, attempt: 0, noShow: false }
    //                     : p
    //             )
    //         );

    //         // Also remove from exceeded attempts if present
    //         setExceededAttemptsPatients(prev =>
    //             prev.filter(p => p.patientID !== patient.patientID)
    //         );

    //         enqueueSnackbar(`Attempts reset successfully for ${patient.fullName}`, { variant: 'success' });
    //     } catch (error) {
    //         console.error("Failed to reset attempts:", error);
    //         const errorMessage = error instanceof Error ? error.message : 'Failed to reset attempts';
    //         enqueueSnackbar(errorMessage, { variant: 'error' });
    //     } finally {
    //         setResettingAttempts(prev => ({ ...prev, [patient.patientID]: false }));
    //     }
    // };

    const handleDeleteTimerAndReport = async (sendReport: boolean) => {
        setOpenReportDialog(false)

        const value = timerValue.current
        await ApiClient.deleteTimer(value)
        setTimerKey((prev) => {
            const newObj = { ...prev }
            delete newObj[value]
            return newObj
        })

        if (sendReport) {
            // send report with doctor ID
            ApiClient.sendReportToSlack(value)
            ApiClient.sendDetailedReportToSlack()
        }
    }

    const handleSendingReportDecision = async (value: string) => {
        timerValue.current = value
        setOpenReportDialog(true)
    }

    useEffect(() => {
        const init = async () => {
            try {
                const queueResponse = await ApiClient.fetchQueue()
                setPatientQueue(() => {
                    return [...queueResponse.patients]
                })
                setTotalPatientsForDay(queueResponse.totalPatientsForDay)
                const timer = await ApiClient.fetchTimerAdmin()
                setTimerKey(() => {
                    return timer
                })

                const drIds = Object.keys(timer)
                if (allDoctors) {
                    for (const ids of drIds) {
                        const dr = allDoctors.find((d) => d.accessID === ids)
                        setDrList((prev) => {
                            return {
                                ...prev,
                                [ids]: dr
                            }

                        })
                    }

                    // Call our helper to ensure we also populate doctor info for patients
                    if (queueResponse.patients.length > 0) {
                        setTimeout(() => updateDrListWithQueueDoctors(), 100);
                    }

                    setIsLoading(false)
                }

                // Fetch patients with exceeded attempts
                await fetchExceededAttemptsPatients();
            } catch (error) {
                setIsLoading(false)
                setError(error as AxiosError<unknown, Error>)
            }
            finally {
                setIsLoading(false)
            }
        }
        init()

        // Set up a refresh interval for exceeded attempts patients
        const refreshInterval = setInterval(fetchExceededAttemptsPatients, 2000); // Refresh every 2 seconds

        return () => {
            clearInterval(refreshInterval);
        };
    }, [allDoctors])

    // const handleEmptyClicked = async () => {
    //     try {
    //         setIsLoading(true)
    //         await ApiClient.emptyQueue()
    //     }
    //     finally {
    //         setIsLoading(false)
    //     }
    // }

    const handleShowDetails = async (patient: PatientQueue) => {
        let detailsArray: MessageLogs[] = []
        if (patientSelected?.patientID === patient.patientID) {
            setPatientSelected(undefined)
            return
        }
        await ApiClient.fetchQueueDetails(patient.patientID).then((details) => {
            details.forEach((value) => {
                const timeline = [
                    {
                        item: 'notificationSentDateTime',
                        time: value.notificationSentDateTime,
                        message: `Notification sent at ${formatTimeWithMeridian(value.notificationSentDateTime)}`
                    },
                    {
                        item: 'joinedAt',
                        time: value.joinedAt,
                        message: `Joined Queue at ${formatTimeWithMeridian(value.joinedAt)}`
                    },
                    {
                        item: 'admittedAt',
                        time: value.admittedAt,
                        message: `Admitted at ${formatTimeWithMeridian(value.admittedAt)}`
                    },
                    {
                        item: 'joinedCallAt',
                        time: value.joinedCallAt,
                        message: `Joined Call at ${formatTimeWithMeridian(value.joinedCallAt)}`
                    },
                    {
                        item: 'completedAt',
                        time: value.completedAt,
                        message: `Dr Completed Consultation at ${formatTimeWithMeridian(value.completedAt)}`
                    },
                    {
                        item: 'leftAt',
                        time: value.leftAt,
                        message: `Left Queue at ${formatTimeWithMeridian(value.leftAt)}`
                    },
                    {
                        item: 'confirmedAt',
                        time: value.confirmedAt,
                        message: `TP Confirmed at ${formatTimeWithMeridian(value.confirmedAt)}`
                    },
                    {
                        item: 'callEndedAt',
                        time: value.callEndedAt,
                        message: `Call Ended At ${formatTimeWithMeridian(value.callEndedAt)}`
                    },
                    {
                        item: 'tech_issue',
                        time: value.tech_issue ? value.callEndedAt : null,
                        message: value.tech_issue ? 'Patient marked with technical issues' : ''
                    }
                ]
                const sortedTimeline = timeline
                    .filter(entry => entry.time !== null && entry.time !== undefined)
                    .sort((a, b) => {
                        const timeA = new Date(a.time as string | number | Date).getTime();
                        const timeB = new Date(b.time as string | number | Date).getTime();
                        return timeA - timeB;
                    })

                detailsArray = [...detailsArray, ...sortedTimeline]

            })
        })

        setDetailsLogs(detailsArray)
        setPatientSelected(patient)


    }

    // add a button under patient who are no shows for the admin to allo them in the queue.
    // Unless they are allowed they cannot be seen by the Doctor.
    // until we define a strict rule to banning them.

    return (
        <>
            {isLoading && <LoadingScreen />}

            <Dialog open={restartTimerDialog} fullWidth={true} maxWidth={'xs'} onClose={() => { setRestartTimerDialog(false) }}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                            Restarting Timer
                        </Typography>
                        <CancelIcon sx={{ width: '100px', height: '100px', color: 'red', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            You cannot restart the 2 minutes timer when a patient is online.
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button sx={{ color: 'green' }} onClick={() => { setRestartTimerDialog(false) }}>
                                Close
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>

            <Dialog open={openReportDialog} fullWidth={true} maxWidth={'xs'} onClose={() => { setOpenReportDialog(false) }}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                            Send Today's Consultation Report
                        </Typography>
                        <StackedBarChartIcon sx={{ width: '100px', height: '100px', color: 'green', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            This will send report to our slack channel.
                        </Typography>
                        <Grid sx={{ mt: 2 }} container spacing={2}>
                            <Button variant='contained' sx={{ color: 'white' }} onClick={() => { handleDeleteTimerAndReport(true) }}>
                                Send Report
                            </Button>
                            <Button variant='contained' sx={{ color: 'white', backgroundColor: 'red' }} onClick={() => { handleDeleteTimerAndReport(false) }}>
                                No Report
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>


            <Grid size={{ lg: 12 }} container>
                <Grid container size={{ lg: 6, xs: 12 }} sx={{ width: '100%', pt: 2 }} justifyContent={'center'} alignItems={'center'} direction={'column'}>
                    {/* <Grid container sx={{ width: { lg: '100%', xs: '100%' }, mb: 2, }} alignItems={'center'} justifyContent={'center'} direction={'column'}>
                        <Typography sx={{ fontSize: '14px', fontWeight: 'bold', mr: 1 }}>
                            PATIENT QUEUES BY DOCTOR
                        </Typography>
                        <Typography sx={{ fontSize: '11px', color: '#666', mt: 0.5 }}>
                            Showing patients with consultations scheduled for today ({new Date().toLocaleDateString('en-AU', {
                                timeZone: 'Australia/Sydney',
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            })})
                        </Typography>
                    </Grid> */}

                    {/* Overall Notification Statistics */}
                    {patientQueue.length > 0 && (
                        <Grid container sx={{ width: { lg: '100%', xs: '100%' }, mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: '8px' }} alignItems={'center'} justifyContent={'center'} direction={'column'}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 'bold', mb: 1, color: '#333' }}>
                                Today's Consultation Overview
                            </Typography>
                            <Typography sx={{ fontSize: '11px', color: '#666', mb: 2, textAlign: 'center' }}>
                                {new Date().toLocaleDateString('en-AU', {
                                    timeZone: 'Australia/Sydney',
                                    weekday: 'long',
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                })}
                            </Typography>
                            <Grid container spacing={3} justifyContent={'center'} alignItems={'center'}>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
                                        Total Patients
                                    </Typography>
                                    <Typography sx={{ fontSize: '20px', fontWeight: 'bold', textAlign: 'center', color: '#333' }}>
                                        {getOverallNotificationStats.totalPatients}
                                    </Typography>
                                </Grid>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
                                        Patients Notified
                                    </Typography>
                                    <Typography sx={{ fontSize: '20px', fontWeight: 'bold', textAlign: 'center', color: 'green' }}>
                                        {getOverallNotificationStats.notifiedPatients}
                                    </Typography>
                                </Grid>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
                                        Notification Ratio
                                    </Typography>
                                    <Typography sx={{
                                        fontSize: '20px',
                                        fontWeight: 'bold',
                                        textAlign: 'center',
                                        color: getOverallNotificationStats.notifiedPatients === getOverallNotificationStats.totalPatients ? 'green' : '#ff9800'
                                    }}>
                                        {getOverallNotificationStats.notificationRatio}
                                    </Typography>
                                </Grid>
                            </Grid>
                            {/* Progress Bar */}
                            <Box sx={{ width: '100%', mt: 2, maxWidth: '300px' }}>
                                <LinearProgress
                                    variant="determinate"
                                    value={getOverallNotificationStats.totalPatients > 0 ? (getOverallNotificationStats.notifiedPatients / getOverallNotificationStats.totalPatients) * 100 : 0}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: '#e0e0e0',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: getOverallNotificationStats.notifiedPatients === getOverallNotificationStats.totalPatients ? 'green' : '#ff9800',
                                            borderRadius: 4,
                                        }
                                    }}
                                />
                                <Typography sx={{ fontSize: '10px', color: '#666', mt: 0.5, textAlign: 'center' }}>
                                    {getOverallNotificationStats.totalPatients > 0 ?
                                        `${Math.round((getOverallNotificationStats.notifiedPatients / getOverallNotificationStats.totalPatients) * 100)}% notified` :
                                        'No patients scheduled'
                                    }
                                </Typography>
                            </Box>
                            {getOverallNotificationStats.notifiedPatients < getOverallNotificationStats.totalPatients && (
                                <Typography sx={{ fontSize: '11px', color: '#666', mt: 1, textAlign: 'center' }}>
                                    {getOverallNotificationStats.totalPatients - getOverallNotificationStats.notifiedPatients} patients pending notification
                                </Typography>
                            )}
                        </Grid>
                    )}
                    <Grid
                        sx={{
                            width: { lg: '80%', xs: '100%' },
                            overflow: 'auto',
                            height: '69vh',
                            pb: 2,
                            '&::-webkit-scrollbar': {
                                width: '6px',
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: '#cbf5dd',
                                borderRadius: '10px',
                                mt: 1,
                                mb: 1
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'green',
                                borderRadius: '10px',
                                p: 2
                            },
                            '&::-webkit-scrollbar-button': {
                                backgroundColor: 'green',
                                height: '7px',
                                borderRadius: '10px'
                            }
                        }}
                    >
                        {patients && nextPatient && patientQueue.length === 0 &&
                            <>
                                <Grid container sx={{ width: '100%', mt: 5 }} justifyContent={'center'}>
                                    <Typography align='center'>
                                        No patients with consultations scheduled for today are currently in the queue.<br />
                                        Patients will appear here when they have consultations scheduled for today and are notified.
                                    </Typography>
                                </Grid>
                            </>}

                        {/* Multi-Doctor Queue Display */}
                        {sortedDoctorIds.length > 0 ? (
                            sortedDoctorIds.map((doctorId) => {
                                const doctorPatients = groupedPatientQueues[doctorId];
                                const stats = getQueueStats(doctorPatients);
                                const notificationStats = getDoctorNotificationStats(doctorPatients);
                                const doctorName = formatDoctorDisplay(doctorId);

                                return (
                                    <Accordion
                                        key={doctorId}
                                        defaultExpanded={true}
                                        sx={{
                                            width: '100%',
                                            mb: 2,
                                            '&:before': {
                                                display: 'none',
                                            },
                                            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
                                        }}
                                    >
                                        <AccordionSummary
                                            expandIcon={<ExpandMoreIcon />}
                                            sx={{
                                                backgroundColor: doctorId === 'unassigned' ? '#f5f5f5' : '#e8f5e8',
                                                '&:hover': {
                                                    backgroundColor: doctorId === 'unassigned' ? '#eeeeee' : '#d4edda',
                                                },
                                                borderRadius: '4px 4px 0 0',
                                            }}
                                        >
                                            <Grid container alignItems="center" justifyContent="space-between" sx={{ width: '100%', pr: 2 }}>
                                                <Grid container direction="column" sx={{ flexGrow: 1 }}>
                                                    <Typography sx={{ fontSize: '14px', fontWeight: 'bold' }}>
                                                        {doctorName}
                                                    </Typography>
                                                    <Typography sx={{
                                                        fontSize: '11px',
                                                        color: notificationStats.notified === notificationStats.total && notificationStats.total > 0 ? 'green' : '#666',
                                                        fontWeight: 'bold'
                                                    }}>
                                                        {notificationStats.notified}/{notificationStats.total} notified, {stats.total} total patients
                                                    </Typography>
                                                </Grid>
                                                <Grid container spacing={1} sx={{ width: 'auto', flexDirection: 'column', alignItems: 'flex-end' }}>
                                                    <Grid container spacing={1} sx={{ width: 'auto' }}>
                                                        {stats.online > 0 && (
                                                            <Typography sx={{ fontSize: '10px', color: 'green', fontWeight: 'bold' }}>
                                                                Online: {stats.online}
                                                            </Typography>
                                                        )}
                                                        {stats.joined > 0 && (
                                                            <Typography sx={{ fontSize: '10px', color: 'blue', fontWeight: 'bold', ml: 1 }}>
                                                                Joined: {stats.joined}
                                                            </Typography>
                                                        )}
                                                        {stats.completed > 0 && (
                                                            <Typography sx={{ fontSize: '10px', color: 'green', fontWeight: 'bold', ml: 1 }}>
                                                                Completed: {stats.completed}
                                                            </Typography>
                                                        )}
                                                        {stats.noShow > 0 && (
                                                            <Typography sx={{ fontSize: '10px', color: 'red', fontWeight: 'bold', ml: 1 }}>
                                                                No-Show: {stats.noShow}
                                                            </Typography>
                                                        )}
                                                    </Grid>
                                                    {/* Notification Progress Indicator */}
                                                    {stats.total > 0 && (
                                                        <Grid container sx={{ width: 'auto', mt: 0.5 }}>
                                                            <Typography sx={{
                                                                fontSize: '9px',
                                                                color: notificationStats.notified === notificationStats.total ? 'green' : '#ff9800',
                                                                fontWeight: 'bold',
                                                                backgroundColor: notificationStats.notified === notificationStats.total ? '#e8f5e8' : '#fff3e0',
                                                                px: 1,
                                                                py: 0.25,
                                                                borderRadius: '8px'
                                                            }}>
                                                                {notificationStats.notified === notificationStats.total ? '✓ All Notified' : `${notificationStats.total - notificationStats.notified} Pending`}
                                                            </Typography>
                                                        </Grid>
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </AccordionSummary>
                                        <AccordionDetails sx={{ p: 1 }}>
                                            {doctorPatients.map((patient) => (
                                                <PatientQueueItem key={patient.patientID} patient={patient} />
                                            ))}
                                        </AccordionDetails>
                                    </Accordion>
                                );
                            })
                        ) : (
                            patientQueue.length > 0 && (
                                <Grid container sx={{ width: '100%', mt: 5 }} justifyContent={'center'}>
                                    <Typography align='center'>
                                        Loading patient assignments...
                                    </Typography>
                                </Grid>
                            )
                        )}
                    </Grid>
                </Grid>

                <Grid container size={{ lg: 6, xs: 12 }} sx={{ width: '100%', pt: 2 }} justifyContent={'center'} alignItems={'center'} direction={'column'}>
                   

                    {/* <Grid container sx={{ width: { lg: '100%', xs: '100%' }, mb: 2, }} alignItems={'center'} justifyContent={'center'} spacing={2}>
                        <Grid sx={{ mb: 1, width: '60%' }}>
                            <Button
                                variant='contained'
                                sx={{ backgroundColor: 'black', textTransform: 'none', width: '100%' }}
                                onClick={handleEmptyClicked}
                            >
                                Empty Queue
                            </Button>
                        </Grid>
                    </Grid> */}


                    <Grid
                        sx={{
                            width: { lg: '80%', xs: '100%' },
                            overflow: 'auto',
                            height: '69vh',
                            pb: 2,
                            '&::-webkit-scrollbar': {
                                width: '6px',
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: '#cbf5dd',
                                borderRadius: '10px',
                                mt: 1,
                                mb: 1
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'green',
                                borderRadius: '10px',
                                p: 2
                            },
                            '&::-webkit-scrollbar-button': {
                                backgroundColor: 'green',
                                height: '7px',
                                borderRadius: '10px'
                            }
                        }}
                    >
                        {Object.keys(timerKey).filter((key) => timerKey[key] !== undefined).length > 0 ? Object.keys(timerKey).filter((key) => timerKey[key] !== undefined).map((value) => {
                            return (
                                <Fragment key={`${timerKey[value]}`}>
                                    <Grid sx={{ width: '100%', maxWidth: "100%" }}>
                                        <Grid
                                            container
                                            justifyContent={'start'}
                                            sx={{
                                                boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                border: '1px solid rgba(0, 0, 0, 0.1)',
                                                p: 1,
                                                mb: 1,
                                                mr: 1
                                            }}
                                            alignItems={'center'}>
                                            <Grid sx={{ flexGrow: 1 }} container direction={'column'}>
                                                <Grid container alignItems={'start'}>
                                                    <Typography >
                                                        {drList[value]?.username || 'No Name'}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                            <Grid sx={{ width: '25%' }}>
                                                <Button
                                                    variant='contained'
                                                    sx={{ backgroundColor: timerKey[value] ? 'red' : 'green', textTransform: 'none', width: '100%' }}
                                                    onClick={async () => {
                                                        if (timerKey[value]) {
                                                            // await ApiClient.deleteTimer(value)
                                                            // setTimerKey((prev) => {
                                                            //     const newObj = { ...prev }
                                                            //     delete newObj[value]
                                                            //     return newObj
                                                            // })
                                                            handleSendingReportDecision(value)
                                                        }
                                                        else {
                                                            if (doctor) {
                                                                const result = await ApiClient.startTimer(value)
                                                                if (result.joinedPatient.length > 0 || result.onlinePatient.length > 0) {
                                                                    setRestartTimerDialog(true)
                                                                }
                                                                else {
                                                                    setTimerKey((prev) => {
                                                                        return {
                                                                            ...prev,
                                                                            [value]: result.timerKey
                                                                        }
                                                                    })
                                                                }
                                                            }

                                                        }

                                                    }}
                                                >
                                                    {timerKey[value] ? "Stop Timer" : "Restart Timer"}
                                                </Button>
                                            </Grid>
                                            <Grid>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Fragment>
                            )
                        }) : <Grid container sx={{ width: '100%' }} alignItems='center' justifyContent={'center'}>
                            No Timer Registered
                        </Grid>}
                          {/* Doctor Status Section */}
                    <Grid sx={{ width: { lg: '100%', xs: '100%' }, mb: 2 }}>
                        <DoctorStatusIndicator />
                    </Grid>

                        {/* Exceeded Attempts Section */}
                        <ExceededAttemptsSection
                            patients={exceededAttemptsPatients}
                            onReset={handlePatientAttemptsReset}
                        />
                    </Grid>
                    

                </Grid>
            </Grid>
        </>
    )
}

export default PatientQueueView
