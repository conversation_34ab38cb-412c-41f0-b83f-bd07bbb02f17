import { Di<PERSON>, Divider, <PERSON><PERSON><PERSON><PERSON>on, Typo<PERSON>, Box, useMediaQuery, Button, Stack } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { OrderHistory, PatientData, PatientOrder, Questionnaire, QuestionnaireShape, TreatmentPlanHistory, HealthCheckShape } from "../../../types";
import { convertTimeStampToReadableFormat, provinceAbbreviation, safeScriptLinksPerProvince } from "../../../utils";
import { useState, useCallback } from "react";
import CloseIcon from '@mui/icons-material/Close';
import Order from "./orders/order";
import QuestionnaireForm from "./questionnaire";
import TreatmentPlan from "./treatmentPlan";
import HealthCheck from "./healthCheck";
import { useTheme } from "@mui/material/styles"
import { usePatient } from "../../../hooks/patient-provider";
import RequestSupply from "./orders/request-to-supply";
import NotificationReceipt from "./orders/notification-of-receipt";
import AuthorizationCollect from "./orders/authorization-to-collect";
import { useSnackbar } from "notistack";


type HistoriesProps = {
    patient: Omit<PatientData, 'consultation'> | undefined
    setSelectedHistory: React.Dispatch<React.SetStateAction<Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined>>
    selectedHistory: Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined
    mobile?: boolean
    heightVh?: string
}

type History = {
    [key: string]: {
        name: string
        element: React.ReactElement
    }
}

type SupplyDocument = {
    title: string
    name: string;
}

type SupplyDocumentElement = {
    [key: string]: {
        element: React.ReactElement
    }
}

const supplyDocuments: SupplyDocument[] = [
    {
        title: 'Request To Supply',
        name: 'request',

    },

    {
        title: 'Notification Of Receipt',
        name: 'notification',

    },
    {
        title: 'Authorization to Collect',
        name: 'authorization',

    }
]

const supplyDocumentElement: SupplyDocumentElement = {
    request: {
        element: <RequestSupply/>
    },
    notification: {
        element: <NotificationReceipt/>
    },
    authorization: {
        element: <AuthorizationCollect/>
    }
}
const HistoryTypes: History = {
    order: {
        name: 'Order',
        element: <Order />
    },
    questionnaire: {
        name: "Questionnaire",
        element: <QuestionnaireForm />
    },
    treatmentplan: {
        name: 'Treatment Plan',
        element: <TreatmentPlan />
    },
    healthCheck: {
        name: 'Health Check - 2 weeks',
        element: <HealthCheck />
    }
}
// Calculate age from date of birth string (format: YYYY-MM-DD or DD/MM/YYYY)
const calculateAge = (dob: string): number => {
    if (!dob) return 0;

    let birthDate: Date;

    // Handle different date formats
    if (dob.includes('-')) {
        // YYYY-MM-DD format
        birthDate = new Date(dob);
    } else if (dob.includes('/')) {
        // DD/MM/YYYY format
        const parts = dob.split('/');
        if (parts.length === 3) {
            // Convert to YYYY-MM-DD for Date constructor
            birthDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
        } else {
            return 0; // Invalid format
        }
    } else {
        return 0; // Unsupported format
    }

    // Check if date is valid
    if (isNaN(birthDate.getTime())) {
        return 0;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    // Adjust age if birthday hasn't occurred yet this year
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
};

const Histories: React.FC<HistoriesProps> = ({  mobile = false}) => {
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const [openHistoryDetails, setOpenHistoryDetails] = useState(false)
    const [openSupplyDocument, setOpenSupplyDocument] = useState(false)
    const { enqueueSnackbar } = useSnackbar();

    const { selectedPatient, patientHistory, setSelectedPatientHistory, selectedPatientHistory, selectedSupplyDocument, setSelectedSupplyDocument } = usePatient()
    const [openDemographyDialog, setOpenDemographyDialog] = useState(false)


    const handleHistoryClicked = (history: QuestionnaireShape | PatientOrder | TreatmentPlanHistory | HealthCheckShape | undefined) => {
        if (mobile) {
            setSelectedPatientHistory(history)
            setOpenHistoryDetails(true)
        }
        else {
            if (history) {
                setSelectedPatientHistory(history)
            }
        }
    }

    const handleSupplyDocumentCLicked = (doc: SupplyDocument) => {
        setSelectedSupplyDocument(doc)
        setOpenSupplyDocument(true)
    }

    const handleCloseHistoryDetails = () => {
        setOpenHistoryDetails(false)
    }

    const getSafeScriptLink = (province: string) => {
        const smallCase = province.toLowerCase();
        return safeScriptLinksPerProvince[smallCase] ?? undefined;
    };

    const getProvinceAbbreviation = (province: string) => {
        const smallCase = province.toLowerCase();
        return provinceAbbreviation[smallCase] ?? undefined;
    };

    // Copy functions for patient information
    const copyToClipboard = useCallback((text: string | undefined, infoType: string) => {
        if (!text) {
            enqueueSnackbar(`No ${infoType} available to copy`, { variant: 'error' });
            return;
        }

        // Check if clipboard API is available
        if (!navigator.clipboard) {
            enqueueSnackbar('Clipboard API not available in your browser', { variant: 'error' });
            return;
        }

        navigator.clipboard.writeText(text)
            .then(() => {
                enqueueSnackbar(`${infoType} copied to clipboard`, {
                    variant: 'success',
                    autoHideDuration: 4000
                });
            })
            .catch(err => {
                enqueueSnackbar(`Failed to copy ${infoType}: ${err}`, { variant: 'error' });
            });
    }, [enqueueSnackbar]);

    const copyFirstName = useCallback(() => {
        // If firstName is available, use it directly
        if (selectedPatient?.firstName) {
            copyToClipboard(selectedPatient.firstName, 'First Name');
            return;
        }

        // If only fullName is available, extract the first name
        if (selectedPatient?.fullName) {
            const nameParts = selectedPatient.fullName.trim().split(' ');
            if (nameParts.length > 0) {
                copyToClipboard(nameParts[0], 'First Name');
                return;
            }
        }

        // If neither is available
        enqueueSnackbar('No first name available to copy', { variant: 'error' });
    }, [selectedPatient, copyToClipboard, enqueueSnackbar]);

    const copyLastName = useCallback(() => {
        // If lastName is available, use it directly
        if (selectedPatient?.lastName) {
            copyToClipboard(selectedPatient.lastName, 'Last Name');
            return;
        }

        // If only fullName is available, extract the last name
        if (selectedPatient?.fullName) {
            const nameParts = selectedPatient.fullName.trim().split(' ');
            if (nameParts.length > 1) {
                copyToClipboard(nameParts[nameParts.length - 1], 'Last Name');
                return;
            }
        }

        // If neither is available
        enqueueSnackbar('No last name available to copy', { variant: 'error' });
    }, [selectedPatient, copyToClipboard, enqueueSnackbar]);

    const copyDOB = useCallback(() => {
        copyToClipboard(selectedPatient?.dob, 'Date of Birth');
    }, [selectedPatient, copyToClipboard]);

    // const getHistoryTypeName = (type: string | undefined): string => {
    //     if (!type) return 'General';

    //     if (HistoryTypes[type]) {
    //         return HistoryTypes[type].name;
    //     }

    //     // Handle chat-treatmentplan case
    //     if (type.includes('treatmentplan')) {
    //         return HistoryTypes['treatmentplan'].name;
    //     }

    //     return 'General';
    // };

    // const getHistoryTypeElement = (type: string | undefined): React.ReactElement | null => {
    //     if (!type) return null;

    //     if (HistoryTypes[type]) {
    //         return HistoryTypes[type].element;
    //     }

    //     // Handle chat-treatmentplan case
    //     if (type.includes('treatmentplan')) {
    //         return HistoryTypes['treatmentplan'].element;
    //     }

    //     return null;
    // };
    const getHistoryTypeName = (type: string | undefined): string => {
        if (!type) return 'General';

        if (HistoryTypes[type]) {
            return HistoryTypes[type].name;
        }

        // Handle chat-treatmentplan case
        if (type.includes('treatmentplan')) {
            return HistoryTypes['treatmentplan'].name;
        }

        return 'General';
    };

    const getHistoryTypeElement = (type: string | undefined): React.ReactElement | null => {
        if (!type) return null;

        if (HistoryTypes[type]) {
            return HistoryTypes[type].element;
        }

        // Handle chat-treatmentplan case
        if (type.includes('treatmentplan')) {
            return HistoryTypes['treatmentplan'].element;
        }

        return null;
    };

    return (
        <>
            <Dialog open={openHistoryDetails} fullWidth maxWidth='md' onClose={handleCloseHistoryDetails}>
                <Box sx={{ pt: 5, pl: 5, pr: 5 }}>
                    <Grid container justifyContent={'center'} alignItems='center'>
                        <Grid sx={{ flexGrow: 1 }}>
                            <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                                {getHistoryTypeName(selectedPatientHistory?.type)}

                            </Typography>
                        </Grid>
                        <Grid>
                            <IconButton onClick={handleCloseHistoryDetails}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                </Box>
                <Box sx={{ padding: 5, overflow: 'auto' }}>
                    <Grid sx={{ mt: 2 }}>
                        {getHistoryTypeElement(selectedPatientHistory?.type)}

                    </Grid>
                </Box>
            </Dialog>
            <Dialog open={openSupplyDocument} fullWidth maxWidth='md' onClose={()=> setOpenSupplyDocument(false)}>
                <Box sx={{ pt: 5, pl: 5, pr: 5 }}>
                    <Grid container justifyContent={'center'} alignItems='center'>
                        <Grid sx={{ flexGrow: 1 }}>
                            <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                                {selectedSupplyDocument?.title}
                            </Typography>
                        </Grid>
                        <Grid>
                            <IconButton onClick={()=> setOpenSupplyDocument(false)}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                </Box>
                <Box sx={{ padding: 5, overflow: 'auto' }}>
                    <Grid sx={{ mt: 2 }}>
                        {selectedSupplyDocument?.name ? supplyDocumentElement[selectedSupplyDocument.name].element : null}
                    </Grid>
                </Box>
            </Dialog>

            <Dialog open={openDemographyDialog} fullWidth maxWidth='md' onClose={() => setOpenDemographyDialog(false)}>
                <Box sx={{ pt: 5, pl: 5, pr: 5 }}>
                    <Grid container justifyContent={'center'} alignItems='center'>
                        <Grid sx={{ flexGrow: 1 }}>
                            <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                                Patient Information
                            </Typography>
                        </Grid>
                        <Grid>
                            <IconButton onClick={() => setOpenDemographyDialog(false)}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                </Box>
                <Box sx={{ pl: 5, mb: 5, overflow: 'auto' }}>
                    <Grid sx={{ mt: 2 }}>
                        <Grid container sx={{ width: '100%' }}>
                            <Grid sx={{ width: '100%' }} container direction={'column'} spacing={1}>
                                <Grid container alignItems={'center'}>
                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '28px', fontWeight: '500' }}>
                                        {`${selectedPatient?.fullName || "No Name"}`}
                                    </Typography>
                                </Grid>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    Email: {selectedPatient?.email}
                                </Typography>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    DOB: {selectedPatient?.dob}
                                </Typography>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    Have used Cannabis Before: {selectedPatient?.usedCannabisBefore ? 'Yes' : 'No'}
                                </Typography>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    State: {selectedPatient?.state}
                                </Typography>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    Mobile: {selectedPatient?.mobile}
                                </Typography>
                                <Grid sx={{ mt: 2 }}>
                                    {
                                        selectedPatient?.state &&
                                        getSafeScriptLink(selectedPatient.state) &&
                                        <Button sx={{ backgroundColor: 'grey', color: 'white', textTransform: 'none' }} onClick={() => {
                                            window.open(
                                                `${selectedPatient.state && getSafeScriptLink(selectedPatient.state)}`, // Replace with your desired URL
                                                "_blank" // Target name
                                                // "width=800,height=600,noopener,noreferrer" // Window features
                                            );
                                        }}>
                                            Go to {getProvinceAbbreviation(selectedPatient.state)}{" "} SafeScript
                                        </Button>
                                    }
                                </Grid>

                            </Grid>
                        </Grid>
                    </Grid>
                </Box>
            </Dialog>


            <Grid sx={{
                borderRadius: 2, p: 1, pt: 0, border: isDesktop ? '' : '1px solid green', mt: { xs: 1, lg: 0 }
            }} container direction={'column'}>
                <Box sx={{ mb: 2 }}>

                    <Box sx={{ width: '100%' }}>
                        <Typography sx={{ fontSize: '12px', fontWeight: 'bold', color: 'green', py: 1, px: 2 }}>
                             {selectedPatient?.fullName}
                        </Typography>
                    </Box>
                    <Box sx={{ width: '100%' }}>
                        <Typography variant="body1" sx={{ fontSize: '12px', py: 1, px: 2 }}>
                            <strong>DOB:</strong> {selectedPatient?.dob || ''} <strong>(Age:</strong> {selectedPatient?.dob ? `${calculateAge(selectedPatient.dob)}` : ''}<strong>)</strong>
                        </Typography>
                    </Box>
                    <Box sx={{ width: '100%' }}>
                        <Typography variant="body1" sx={{ fontSize: '12px', py: 1, px: 2 }}>
                            <strong>Mobile:</strong> {selectedPatient?.mobile || selectedPatient?.phone}
                        </Typography>
                    </Box>
                    <Box sx={{ width: '100%' }}>
                        <Typography variant="body1" sx={{ fontSize: '12px', py: 1, px: 2 }}>
                            <strong>State:</strong> {selectedPatient?.state || ''}
                        </Typography>

                    </Box>
                    <Box sx={{ width: '100%', py: 1, px: 2 }}>

                    {
                        selectedPatient?.state &&
                        getSafeScriptLink(selectedPatient.state) &&
                        <Button sx={{ backgroundColor: 'grey', color: 'white', textTransform: 'none', mb: 1 }} onClick={() => {
                            window.open(
                                `${selectedPatient.state && getSafeScriptLink(selectedPatient.state)}`,
                                "_blank"
                            );
                        }}>
                            Go to {getProvinceAbbreviation(selectedPatient.state)}{" "} SafeScript
                        </Button>
                    }

                    <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={0.3}
                        sx={{ mt: 0.5 }}
                        useFlexGap
                        flexWrap="wrap"
                    >
                        {(selectedPatient?.firstName || selectedPatient?.fullName) && (
                            <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyFirstName}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '110px', height: '22px' }}
                            >
                                Copy First Name
                            </Button>
                        )}
                        {(selectedPatient?.lastName || selectedPatient?.fullName) && (
                            <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyLastName}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '110px', height: '22px' }}
                            >
                                Copy Last Name
                            </Button>
                        )}
                        {selectedPatient?.dob && (
                            <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyDOB}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '90px', height: '22px' }}
                            >
                                Copy DOB
                            </Button>
                        )}
                    </Stack>

                    </Box>

                </Box>
                <Divider sx={{ mb: 1 }} />
                <Grid container justifyContent={'center'} sx={{ mb: 1 }}>
                    <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                        Patient History
                    </Typography>
                </Grid>
                <Divider sx={{ mb: 1 }} />
                <Grid sx={{
                    overflowY: 'auto',

                    '&::-webkit-scrollbar': {
                        width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                        backgroundColor: '#cbf5dd',
                        borderRadius: '10px',
                        mt: 1,
                        mb: 1
                    },
                    '&::-webkit-scrollbar-thumb': {
                        backgroundColor: 'green',
                        borderRadius: '10px',
                        p: 2
                    },
                    '&::-webkit-scrollbar-button': {
                        backgroundColor: 'green',
                        height: '7px',
                        borderRadius: '10px'
                    },
                }}>
                    <>
                        {!patientHistory || patientHistory && patientHistory.length <= 0 ? <><Typography sx={{ fontSize: '12px' }} align='center'>No previous history</Typography></> : <></>}
                        {
                            patientHistory.map((history, index) => {

                                // if the history type is an order return the 4 blocks
                                if (history.type === 'order') {
                                    return (
                                        <>
                                            <Grid container direction='column'>
                                                <Grid>
                                                    <div key={`${index}`} onClick={() => handleHistoryClicked(history)} style={{
                                                        cursor: 'pointer',
                                                        width: '100%',
                                                    }}>
                                                        <Grid container
                                                            sx={{
                                                                boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                                p: 1,
                                                                mr: '4px',
                                                                border: selectedPatientHistory?.id === history?.id ? '1px solid green' : '1px solid rgba(0, 0, 0, 0.2)',
                                                                borderRadius: 2,
                                                                mb: 1,

                                                            }}
                                                            direction={"column"}
                                                            key={`${index}`}
                                                        >
                                                            <Grid container sx={{ width: '100%' }}>
                                                                <Grid sx={{ flexGrow: 1, width: '100%' }} container direction={'column'}>
                                                                    <Grid container alignItems={'center'}>
                                                                        <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                                            {getHistoryTypeName(history?.type)}

                                                                        </Typography>
                                                                    </Grid>
                                                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                                                        {
                                                                            convertTimeStampToReadableFormat(history?.updatedAt)
                                                                        }
                                                                    </Typography>
                                                                </Grid>
                                                            </Grid>
                                                        </Grid>
                                                    </div>
                                                </Grid>

                                                <Grid sx={{ border: '1px solid rgba(0, 0, 0, 0.53)', borderRadius: 2, p: 1, mb: 1}} container direction={'column'}>

                                                    <Grid sx={{mb: 2}}>
                                                        <Typography sx={{color: 'green', fontWeight: '500'}}>
                                                            Order Supply Documents
                                                        </Typography>
                                                    </Grid>

                                                    <Grid>
                                                     {supplyDocuments.map((doc, index) => {
                                                        return (
                                                            <div key={`${index}`} onClick={() => {
                                                                handleSupplyDocumentCLicked(doc)
                                                                setSelectedPatientHistory(history)
                                                            }} style={{
                                                                cursor: 'pointer',
                                                                width: '100%',
                                                            }}>
                                                                <Grid container
                                                                    sx={{
                                                                        boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                                        p: 1,
                                                                        mr: '4px',
                                                                        border: '1px solid rgba(0, 0, 0, 0.2)',
                                                                        borderRadius: 2,
                                                                        mb: 1,


                                                                    }}
                                                                    direction={"column"}
                                                                    key={`${index}`}
                                                                >
                                                                    <Grid container sx={{ width: '100%' }}>
                                                                        <Grid sx={{ flexGrow: 1, width: '100%' }} container direction={'column'}>
                                                                            <Grid container alignItems={'center'}>
                                                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                                                    {doc.title}
                                                                                </Typography>
                                                                            </Grid>
                                                                            <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                                                                {
                                                                                    convertTimeStampToReadableFormat(history?.updatedAt)
                                                                                }
                                                                            </Typography>
                                                                        </Grid>
                                                                    </Grid>
                                                                </Grid>
                                                            </div>
                                                        )
                                                     })}
                                                    </Grid>
                                                </Grid>
                                            </Grid>
                                        </>
                                    )
                                }
                                else {
                                    return (
                                        <div key={`${index}`} onClick={() => handleHistoryClicked(history)} style={{
                                            cursor: 'pointer',
                                            width: '100%',
                                        }}>
                                            <Grid container
                                                sx={{
                                                    boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                    p: 1,
                                                    mr: '4px',
                                                    border: selectedPatientHistory?.id === history?.id ? '1px solid green' : '1px solid rgba(0, 0, 0, 0.2)',
                                                    borderRadius: 2,
                                                    mb: 1,
                                                }}
                                                direction={"column"}
                                                key={`${index}`}
                                            >
                                                <Grid container sx={{ width: '100%' }}>
                                                    <Grid sx={{ flexGrow: 1, width: '100%' }} container direction={'column'}>
                                                        <Grid container alignItems={'center'}>
                                                            <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                                {getHistoryTypeName(history?.type)}

                                                            </Typography>
                                                        </Grid>
                                                        <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                                            {
                                                                convertTimeStampToReadableFormat(history?.updatedAt)
                                                            }
                                                        </Typography>
                                                    </Grid>
                                                </Grid>
                                            </Grid>
                                        </div>
                                    )
                                }
                            })
                        }
                    </>
                </Grid>
            </Grid >
        </>
    )
}

export default Histories

