/**
 * Timer Persistence Test Utilities
 * 
 * This file contains test utilities and validation functions for the timer persistence system.
 * These can be used in browser console or automated tests to verify functionality.
 */

import {
  ConsultationTimerState,
  saveTimerState,
  loadTimerState,
  clearTimerState,
  clearAllTimerStates,
  createInitialTimerState,
  calculateRemainingTime,
  isConsultationExpired,
  getRemainingConsultationTime
} from './timer-persistence';

/**
 * Test data generator for timer states
 */
export const createTestTimerState = (
  consultationId: string = 'test-consultation-123',
  doctorId: string = 'test-doctor-456'
): ConsultationTimerState => {
  return createInitialTimerState(consultationId, doctorId, 6);
};

/**
 * Test timer persistence basic functionality
 */
export const testBasicPersistence = (): boolean => {
  console.log('🧪 Testing basic timer persistence...');
  
  const testState = createTestTimerState();
  const consultationId = testState.consultationId;
  
  try {
    // Save state
    saveTimerState(testState);
    console.log('✅ Timer state saved successfully');
    
    // Load state
    const loadedState = loadTimerState(consultationId);
    console.log('✅ Timer state loaded successfully');
    
    // Verify state matches
    if (!loadedState) {
      console.error('❌ Loaded state is null');
      return false;
    }
    
    if (loadedState.consultationId !== testState.consultationId) {
      console.error('❌ Consultation ID mismatch');
      return false;
    }
    
    if (loadedState.doctorId !== testState.doctorId) {
      console.error('❌ Doctor ID mismatch');
      return false;
    }
    
    console.log('✅ State verification passed');
    
    // Clean up
    clearTimerState(consultationId);
    console.log('✅ Cleanup completed');
    
    return true;
  } catch (error) {
    console.error('❌ Basic persistence test failed:', error);
    return false;
  }
};

/**
 * Test timer cleanup functionality
 */
export const testTimerCleanup = (): boolean => {
  console.log('🧪 Testing timer cleanup...');
  
  try {
    // Create multiple timer states
    const states = [
      createTestTimerState('consultation-1', 'doctor-1'),
      createTestTimerState('consultation-2', 'doctor-2'),
      createTestTimerState('consultation-3', 'doctor-1')
    ];
    
    // Save all states
    states.forEach(state => saveTimerState(state));
    console.log('✅ Multiple timer states saved');
    
    // Verify all states exist
    const loadedStates = states.map(state => loadTimerState(state.consultationId));
    if (loadedStates.some(state => state === null)) {
      console.error('❌ Some states failed to load');
      return false;
    }
    console.log('✅ All states loaded successfully');
    
    // Clear specific state
    clearTimerState('consultation-1');
    const clearedState = loadTimerState('consultation-1');
    if (clearedState !== null) {
      console.error('❌ Specific state cleanup failed');
      return false;
    }
    console.log('✅ Specific state cleanup successful');
    
    // Verify other states still exist
    const remainingStates = [
      loadTimerState('consultation-2'),
      loadTimerState('consultation-3')
    ];
    if (remainingStates.some(state => state === null)) {
      console.error('❌ Other states were incorrectly cleared');
      return false;
    }
    console.log('✅ Other states preserved correctly');
    
    // Clear all states
    clearAllTimerStates();
    const allClearedStates = states.map(state => loadTimerState(state.consultationId));
    if (allClearedStates.some(state => state !== null)) {
      console.error('❌ Clear all states failed');
      return false;
    }
    console.log('✅ Clear all states successful');
    
    return true;
  } catch (error) {
    console.error('❌ Timer cleanup test failed:', error);
    return false;
  }
};

/**
 * Test time calculation functions
 */
export const testTimeCalculations = (): boolean => {
  console.log('🧪 Testing time calculations...');
  
  try {
    const now = Date.now();
    const fiveSecondsAgo = now - 5000;
    
    // Test remaining time calculation
    const remaining = calculateRemainingTime(30, fiveSecondsAgo);
    if (remaining !== 25) {
      console.error(`❌ Remaining time calculation failed. Expected 25, got ${remaining}`);
      return false;
    }
    console.log('✅ Remaining time calculation correct');
    
    // Test expired timer detection
    const expiredState = createTestTimerState();
    expiredState.consultation.startTime = now - (7 * 60 * 1000); // 7 minutes ago
    
    if (!isConsultationExpired(expiredState)) {
      console.error('❌ Expired timer detection failed');
      return false;
    }
    console.log('✅ Expired timer detection correct');
    
    // Test active timer detection
    const activeState = createTestTimerState();
    activeState.consultation.startTime = now - (3 * 60 * 1000); // 3 minutes ago
    
    if (isConsultationExpired(activeState)) {
      console.error('❌ Active timer incorrectly detected as expired');
      return false;
    }
    console.log('✅ Active timer detection correct');
    
    // Test remaining consultation time
    const remainingConsultation = getRemainingConsultationTime(activeState);
    const expectedRemaining = 3 * 60; // 3 minutes in seconds
    if (Math.abs(remainingConsultation - expectedRemaining) > 2) { // Allow 2 second tolerance
      console.error(`❌ Remaining consultation time incorrect. Expected ~${expectedRemaining}, got ${remainingConsultation}`);
      return false;
    }
    console.log('✅ Remaining consultation time calculation correct');
    
    return true;
  } catch (error) {
    console.error('❌ Time calculations test failed:', error);
    return false;
  }
};

/**
 * Test countdown state management
 */
export const testCountdownStates = (): boolean => {
  console.log('🧪 Testing countdown state management...');
  
  try {
    const testState = createTestTimerState();
    
    // Test final countdown activation
    testState.countdowns.finalCountdown = {
      active: true,
      seconds: 45,
      startTime: Date.now() - 15000 // Started 15 seconds ago
    };
    
    saveTimerState(testState);
    const loadedState = loadTimerState(testState.consultationId);
    
    if (!loadedState?.countdowns.finalCountdown.active) {
      console.error('❌ Final countdown state not preserved');
      return false;
    }
    
    if (Math.abs(loadedState.countdowns.finalCountdown.seconds - 45) > 1) {
      console.error('❌ Final countdown seconds not preserved correctly');
      return false;
    }
    console.log('✅ Final countdown state preserved correctly');
    
    // Test pause state
    testState.pauseState = {
      isPaused: true,
      pausedAt: Date.now(),
      activeTriggers: ['typing', 'focus'],
      prePauseValues: {
        patientDropout: 25,
        patientNotJoined: 20,
        finalCountdown: 45,
        bufferCountdown: 0
      }
    };
    
    saveTimerState(testState);
    const pausedState = loadTimerState(testState.consultationId);
    
    if (!pausedState?.pauseState.isPaused) {
      console.error('❌ Pause state not preserved');
      return false;
    }
    
    if (pausedState.pauseState.activeTriggers.length !== 2) {
      console.error('❌ Active triggers not preserved');
      return false;
    }
    console.log('✅ Pause state preserved correctly');
    
    // Cleanup
    clearTimerState(testState.consultationId);
    
    return true;
  } catch (error) {
    console.error('❌ Countdown states test failed:', error);
    return false;
  }
};

/**
 * Run all tests
 */
export const runAllTests = (): boolean => {
  console.log('🚀 Running all timer persistence tests...');
  
  const tests = [
    { name: 'Basic Persistence', test: testBasicPersistence },
    { name: 'Timer Cleanup', test: testTimerCleanup },
    { name: 'Time Calculations', test: testTimeCalculations },
    { name: 'Countdown States', test: testCountdownStates }
  ];
  
  let allPassed = true;
  const results: { name: string; passed: boolean }[] = [];
  
  for (const { name, test } of tests) {
    console.log(`\n--- Testing ${name} ---`);
    const passed = test();
    results.push({ name, passed });
    if (!passed) {
      allPassed = false;
    }
  }
  
  console.log('\n📊 Test Results Summary:');
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  console.log(`\n${allPassed ? '🎉' : '💥'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return allPassed;
};

/**
 * Validate localStorage state for current consultation
 */
export const validateCurrentConsultationState = (consultationId: string): void => {
  console.log(`🔍 Validating timer state for consultation: ${consultationId}`);
  
  const state = loadTimerState(consultationId);
  
  if (!state) {
    console.log('ℹ️ No timer state found for this consultation');
    return;
  }
  
  console.log('📋 Timer State Details:');
  console.log(`  Consultation ID: ${state.consultationId}`);
  console.log(`  Doctor ID: ${state.doctorId}`);
  console.log(`  Duration: ${state.consultation.durationMinutes} minutes`);
  console.log(`  Started: ${new Date(state.consultation.startTime).toLocaleTimeString()}`);
  console.log(`  Expired: ${isConsultationExpired(state) ? 'Yes' : 'No'}`);
  
  if (!isConsultationExpired(state)) {
    const remaining = getRemainingConsultationTime(state);
    console.log(`  Remaining: ${Math.floor(remaining / 60)}:${(remaining % 60).toString().padStart(2, '0')}`);
  }
  
  console.log('📊 Active Countdowns:');
  Object.entries(state.countdowns).forEach(([type, countdown]) => {
    if (countdown.active) {
      const remaining = calculateRemainingTime(countdown.seconds, countdown.startTime);
      console.log(`  ${type}: ${remaining}s remaining`);
    }
  });
  
  if (state.pauseState.isPaused) {
    console.log('⏸️ Timers are currently paused');
    console.log(`  Paused at: ${new Date(state.pauseState.pausedAt).toLocaleTimeString()}`);
    console.log(`  Active triggers: ${state.pauseState.activeTriggers.join(', ')}`);
  }

  console.log(`  Last updated: ${new Date(state.lastUpdated).toLocaleTimeString()}`);
  console.log(`  Version: ${state.version}`);

  // Check for action timer requirements
  if (state.countdowns.patientNotJoined.active) {
    console.log('⚠️ Patient Not Joined countdown requires action timer restoration');
  }
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).timerPersistenceTests = {
    runAllTests,
    testBasicPersistence,
    testTimerCleanup,
    testTimeCalculations,
    testCountdownStates,
    validateCurrentConsultationState
  };
}
