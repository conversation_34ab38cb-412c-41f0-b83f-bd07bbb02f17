import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, Divider, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
// import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import VideoCallIcon from '@mui/icons-material/VideoCall';
import CalendarMonthOutlinedIcon from '@mui/icons-material/CalendarMonthOutlined';
import { usePatient } from "../../hooks/patient-provider";
import { useEffect, useState } from "react";
import { PatientData } from "../../types";
import {
    // Link as LocationLink,
    useNavigate
} from '@tanstack/react-location'
import { getConsultationDate, UserActions } from '../../utils/index'
import { ApiClient } from "../../services";
import { AxiosError } from "axios";
import LoadingScreen from "../../utils/loading-screen";
import { useAuth } from "../../hooks/auth-provider";
import VideoCameraFrontIcon from '@mui/icons-material/VideoCameraFront';

const PatientQueue: React.FC = () => {
    const {
        patients,
        updateLockStatus,
        selectedPatient,
        setError,
        setSelectedPatient,
        drId,
        setDrId,
        setPatient,
        timerKey,
        setTimerKey,
        // fetchHistory,
        drList,
        allDoctors,
        setDrList,
    } = usePatient()

    const [isLoading, setIsLoading] = useState(true)
    const [selectedUserDialog, setSelectedUserDialog] = useState(false)
    const [patientStatusMap, setPatientStatusMap] = useState<Record<string, boolean>>({})
    let intervalsFetchPatient: NodeJS.Timeout | undefined = undefined

    const navigate = useNavigate()

    const { doctor } = useAuth()

    const formatDoctorDisplay = (doctorId?: string | null) => {
        if (!doctorId) return 'Unassigned';
        return drList[doctorId]?.username || drList[doctorId]?.name || 'Unassigned';
    };

    const getStatusMessage = (patient: PatientData, realTimeStatus?: boolean) => {
        if (patient.assignedDoctorID && drId !== patient.assignedDoctorID) {
            return `Doctor: ${formatDoctorDisplay(patient.assignedDoctorID)}`
        }
        if (patient.assignedDoctorID && drId === patient.assignedDoctorID) {
            return 'Selected by you.'
        }

        // Use real-time status if provided, otherwise fall back to cached data
        const isOnline = realTimeStatus !== undefined ? realTimeStatus : !!patient.consultation?.meetingOngoing;

        if (!isOnline) {
            return 'Offline'
        }
        if (isOnline) {
            return 'Online'
        }
        return ''
    }

    // Filter patients based on user role - admin/superAdmin see all, doctors see only assigned patients
    const filterPatientsForDoctor = (patientList: PatientData[]): PatientData[] => {
        // Filter out Zelda patients for all users (doctors should never see Zelda)
        const nonZeldaPatients = patientList.filter(patient =>
            patient.email !== '<EMAIL>' && patient.fullName !== 'Zelda Xray'
        );

        // Admin users can see all non-Zelda patients
        if (doctor?.role === 'admin' || doctor?.role === 'superAdmin') {
            return nonZeldaPatients;
        }

        // Regular doctors only see patients assigned to them (excluding Zelda)
        const currentDoctorID = doctor?.accessID || drId;

        return nonZeldaPatients.filter(patient => {
            const assignedDoctorID = patient.assignedDoctorID;
            const isValidAssignment = assignedDoctorID &&
                                    assignedDoctorID !== null &&
                                    assignedDoctorID !== 'null' &&
                                    assignedDoctorID !== '';

            return isValidAssignment && assignedDoctorID === currentDoctorID;
        });
    }

    // Effect to fetch real-time patient status when patients change
    useEffect(() => {
        if (patients && patients.length > 0) {
            const patientIDs = patients.map(p => p.patientID);
            ApiClient.getBatchPatientStatus(patientIDs)
                .then(statusMap => {
                    setPatientStatusMap(statusMap);
                })
                .catch((error: unknown) => {
                    console.error('Error fetching batch patient status:', error);
                    // Keep existing cached status on error
                });
        }
    }, [patients]);

    const handleAdmitClicked = async (patient: PatientData) => {
        // Check if patient has exceeded attempts (business rule: attempt > 0 AND noShow = true)
        if (patient.noShow === true && (patient.attempt ?? 0) >= 1) {
            await ApiClient.userActions('Doctor', patient.patientID || '', UserActions.TIMEOUT,
                `Manual admission blocked - patient has noShow: true, attempts: ${patient.attempt}`);
            return; // Block admission
        }

        setIsLoading(true)
        setSelectedPatient(patient)
        // fetch treatmentplans by patient email
        // fetch orders by patient email
        //fetchHistory(patient)

        const drIdLocal = localStorage.getItem('xdr')
        const doctorID = drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : ""

        const verifyLocked = await ApiClient.verifyLockedPatient(patient.patientID)

        // Check if the patient is assigned to this doctor or not assigned to any doctor
        if (!verifyLocked || !verifyLocked.assignedDoctorID || verifyLocked.assignedDoctorID === doctorID) {
            try {
                await ApiClient.alertAwayPatientOnAdmit(patient)
                await ApiClient.postRedirect(patient)
                await ApiClient.postPatientAdmission(patient.patientID, doctorID)

                // Update the patient's assignedDoctorID in the PatientQueue table
                updateLockStatus(patient, true, doctorID)

                // Update the patient object with the assignedDoctorID
                patient.assignedDoctorID = doctorID

                navigate({ to: '/doctor-consultation', search: { token: patient.patientID } })
            } catch (error: any) {
                setIsLoading(false)
                if (error?.response?.data?.message === 'PATIENT_EXCEEDED_ADMISSION_LIMIT') {
                    await ApiClient.userActions('Doctor', patient.patientID || '', UserActions.TIMEOUT,
                        'Manual admission blocked - patient exceeded admission limit');
                } else {
                    throw error; // Re-throw other errors
                }
            }
        }
        else {
            setIsLoading(false)
            setSelectedUserDialog(true)
        }
    }

    useEffect(() => {
        const handleVisibilityChange = async () => {
            if (document.visibilityState === 'visible') {
                await ApiClient.userActions('Doctor', '', UserActions.RETURNED, `Doctor ${doctor?.username} returned to all consultation screen`)
            }
            else if (document.visibilityState === 'hidden') {
                await ApiClient.userActions('Doctor', '', UserActions.AWAY, `Doctor ${doctor?.username} left all consultation screen`)
            }
        }

        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        }

    }, [])

    useEffect(() => {
        const fetchPatient = async () => {

            try {
                await ApiClient.userActions('Doctor', '', UserActions.REACHED, `Doctor ${doctor?.username} looking at all consultations`)
                const patients = await ApiClient.getPatients()
                setPatient(() => {
                    return patients?.sort((a, b) => {
                        // Primary sort: meetingOngoing
                        if (a.consultation?.meetingOngoing !== b.consultation?.meetingOngoing) {
                            return a.consultation?.meetingOngoing ? -1 : 1;
                        }

                        // Secondary sort: consultationStart (earliest first)
                        if (a.consultation?.consultationDate && b.consultation?.consultationDate) {
                            return new Date(a.consultation.consultationDate).getTime() - new Date(b.consultation.consultationDate).getTime();
                        }

                        return 0; // Maintain order for other cases
                    });
                })
                const drIdLocal = localStorage.getItem('xdr')
                if (drIdLocal) {
                    setDrId(drIdLocal)
                }
                setIsLoading(false)
            } catch (error) {
                setIsLoading(false)
                setError(error as AxiosError<unknown, Error>)
            }
        }
        fetchPatient()

        // ✅ Set up periodic refresh to ensure fresh patient data
        // This prevents stale data issues when patients are marked as no-show
        const refreshInterval = setInterval(fetchPatient, 10000); // Refresh every 10 seconds

        return () => {
            clearInterval(refreshInterval);
        };
    }, [])

    useEffect(() => {
        const init = async () => {
            try {
                if (selectedPatient) {
                    localStorage.removeItem('TpPlan')
                    let drIdLocal = null
                    if (!drId) {
                        drIdLocal = localStorage.getItem('xdr')
                    }
                    else if (!drIdLocal) {
                        drIdLocal = doctor?.accessID
                    }

                    if (drIdLocal) {
                        updateLockStatus(selectedPatient, false, drIdLocal)
                        await ApiClient.updateMeetingStatus(selectedPatient.patientID, false)
                    }

                    setSelectedPatient(undefined)
                }

                if (doctor && doctor.role !== 'admin') {
                    const timer = await ApiClient.fetchTimer(doctor.accessID)
                    setTimerKey((prev) => {
                        return {
                            ...prev,
                            [timer.timerKey]: timer.timerKey
                        }
                    })
                }
            }
            catch (error) {
                setError(error as AxiosError<unknown, Error>)
            }
        }
        init()
    }, [])

    useEffect(() => {
        const autoAdmitDoc = async () => {
            if (!intervalsFetchPatient) {
                intervalsFetchPatient = setInterval(async () => {
                    // Make sure the shift has started before redirecting people.
                    if (doctor?.accessID && timerKey[doctor.accessID]) {
                        const result = await ApiClient.fetchNextPatientToConsult(doctor.accessID)
                        const nextPatient = result.length > 0 ? result[0] : undefined
                        if (nextPatient) {
                            // Use fresh patient data from API instead of stale local state
                            // This ensures we have the latest attempt and noShow values
                            const freshPatientData: PatientData = {
                                ...nextPatient,
                                assignedDoctorID: nextPatient.assignedDoctorID || undefined
                            }

                            // Admit the patient with fresh data
                            handleAdmitClicked(freshPatientData)
                        }
                    }
                }, 2000)
            }
        }

        autoAdmitDoc()

        return () => {
            clearInterval(intervalsFetchPatient)
        }

    }, [patients, timerKey])

    // Ensure drList is populated for all assignedDoctorID values
    useEffect(() => {
        if (!patients || !allDoctors) return;
        const doctorMap = { ...drList };
        patients.forEach((patient) => {
            const doctorId = patient.assignedDoctorID;
            if (doctorId && !doctorMap[doctorId]) {
                const found = allDoctors.find((d) => d.accessID === doctorId);
                if (found) {
                    doctorMap[doctorId] = found;
                }
            }
        });
        setDrList(doctorMap);
    }, [patients, allDoctors]);

    return (
        <>
            {isLoading && <LoadingScreen />}

            <Dialog open={selectedUserDialog} fullWidth={true} maxWidth={'xs'} onClose={() => { setSelectedUserDialog(false) }}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                            Patient already selected
                        </Typography>
                        <VideoCameraFrontIcon sx={{ width: '100px', height: '100px', color: 'red', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            This patient has been selected by another Doctor. But it has not reflected yet on your side. <br /><br />Please, Select the next patient
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button sx={{ color: 'green' }} onClick={() => { setSelectedUserDialog(false) }}>
                                Close
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>

            <Grid sx={{
                border: '1px solid green', borderRadius: 2, p: 1, display: 'flex',
                flexGrow: 1,
            }} container direction={'column'}>
                <Grid container justifyContent={'center'} alignItems={'center'} sx={{ mb: 1 }}>

                    <Grid>
                        <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                            Total: {filterPatientsForDoctor(patients || []).length}
                        </Typography>
                    </Grid>

                    <Grid justifyContent="center" container alignItems='center' sx={{ flexGrow: 1, ml: doctor?.role === 'doctor' ? 5 : -5 }}>
                        <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                            Today's Consultations
                        </Typography>
                    </Grid>

                    <Grid>
                        {doctor?.role === 'doctor' && <>
                            <Button variant="contained" disabled={doctor?.accessID && timerKey[doctor.accessID] ? true : false} sx={{ textTransform: 'none', backgroundColor: 'green' }} onClick={async () => {
                                if (doctor) {
                                    const result = await ApiClient.startTimer(doctor.accessID)
                                    setTimerKey((prev) => {
                                        return {
                                            ...prev,
                                            [doctor.accessID]: result.timerKey
                                        }
                                    })
                                }
                            }}>
                                Start Shift
                            </Button>
                        </>}

                    </Grid>
                </Grid>

                <Divider sx={{ mb: 2 }} />
                <Grid sx={{
                    overflowY: 'auto',
                    height: '75dvh',
                    '&::-webkit-scrollbar': {
                        width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                        backgroundColor: '#cbf5dd',
                        borderRadius: '10px',
                        mt: 1,
                        mb: 1
                    },
                    '&::-webkit-scrollbar-thumb': {
                        backgroundColor: 'green',
                        borderRadius: '10px',
                        p: 2
                    },
                    '&::-webkit-scrollbar-button': {
                        backgroundColor: 'green',
                        height: '7px',
                        borderRadius: '10px'
                    },
                }}>
                    <>
                        {
                            filterPatientsForDoctor(patients || []).map((patient, index) => {
                                return (
                                    <Grid container sx={{
                                        boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                        p: 1,
                                        mr: '4px',
                                        border: selectedPatient?.patientID === patient.patientID ? '1px solid green' : '1px solid rgba(0, 0, 0, 0.2)',
                                        borderRadius: 2,
                                        mb: 1,
                                        '&:hover': {
                                            visibility: 'visible',
                                            border: '1px solid green',
                                        }
                                    }}
                                        direction={"column"}
                                        key={`${index}`}
                                    >
                                        <Grid container>
                                            <Grid sx={{ flexGrow: 1 }} container direction={'column'}>
                                                <Grid container alignItems={'center'}>
                                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                        {`${patient.fullName}`}
                                                    </Typography>
                                                </Grid>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                                    {
                                                        getStatusMessage(patient, patientStatusMap[patient.patientID])
                                                    }
                                                </Typography>
                                            </Grid>
                                            <Grid direction={'row'} alignItems={'center'} justifyContent={'center'} container>
                                                <Grid sx={{ mr: 1 }} container>
                                                    <VideoCallIcon style={{
                                                        color: (patientStatusMap[patient.patientID] !== undefined
                                                            ? patientStatusMap[patient.patientID]
                                                            : !!patient.consultation?.meetingOngoing) ? 'green' : 'grey'
                                                    }} />
                                                </Grid>
                                                <Grid>
                                                    <Button
                                                        variant="contained"
                                                        sx={{
                                                            textTransform: 'none', transform: 'none', borderRadius: '0.50rem',
                                                            width: '25px',
                                                            height: '25px',
                                                            outline: "none",
                                                            "&:focus": { outline: "none" },
                                                            "&:focusVisible": { outline: "none" },
                                                            fontSize: '12px',
                                                            backgroundColor: patient.consultation?.meetingOngoing ? 'green' : 'grey'
                                                        }}
                                                        onClick={async () => {
                                                            await ApiClient.userActions('Doctor', patient.patientID || '', UserActions.ADMITTED, `Doctor ${doctor?.username} manually admitted this patient`)
                                                            handleAdmitClicked(patient)
                                                        }}
                                                        disabled={
                                                            (doctor?.accessID && !timerKey[doctor.accessID]) ||
                                                            (patient.assignedDoctorID && drId !== patient.assignedDoctorID) ||
                                                            (patient.noShow === true && (patient.attempt ?? 0) >= 1)
                                                        }
                                                    >
                                                        Admit
                                                    </Button>
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                        <Grid sx={{ mt: 2 }} container justifyContent={'start'} alignItems={'center'}>
                                            <CalendarMonthOutlinedIcon sx={{ height: '20px', width: '20px', mr: 1 }} />
                                            <Typography sx={{ fontSize: '0.70rem' }}>
                                                {getConsultationDate(patient.consultation?.consultationDate, patient.consultation?.consultationEnd)}
                                            </Typography>
                                        </Grid>
                                        <Grid container justifyContent={'start'} alignItems={'center'} sx={{ mt: 2 }}>
                                            <Grid sx={{ flexGrow: 1 }} container justifyContent={'start'} alignItems={'center'}>
                                                <Typography sx={{ fontSize: '12px' }} display={'inline'}>
                                                    {patient.returningPatient ? <span style={{ fontWeight: 'bold' }}>Returning Patient</span> : 'New Patient'}
                                                </Typography>
                                            </Grid>
                                            {/* {patient.returningPatient && <Grid container justifyContent={'center'} alignItems={'center'}>
                                                <LocationLink to={'/patient-history'} search={{ token: patient.patientID }}>
                                                    <Button
                                                        sx={{ fontSize: '12px', color: 'green', fontWeight: 'bold', mr: 1, textTransform: 'none' }}
                                                        component='button'
                                                        endIcon={<ArrowForwardIcon sx={{ width: '12px', ml: 0.5 }} />}
                                                    >
                                                        View History
                                                    </Button>
                                                </LocationLink>
                                            </Grid>} */}
                                        </Grid>
                                    </Grid>
                                )
                            })
                        }
                    </>
                </Grid>
            </Grid>
        </>
    )
}

export default PatientQueue