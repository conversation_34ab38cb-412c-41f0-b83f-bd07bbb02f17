import React, { useState } from "react";
import Grid from "@mui/material/Grid2";
import { Box, Button, useMediaQuery, Divider, FormControl, IconButton, InputAdornment, InputLabel, OutlinedInput, TextField, Typography, FormHelperText } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useTheme } from "@mui/material/styles"
import { loginData } from "../../types";
import { useAuth } from "../../hooks/auth-provider";
import LoadingScreen from "../../utils/loading-screen";

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '30px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

const Login: React.FC = () => {
    const [showPassword, setShowPassword] = useState(false);
    const handleClickShowPassword = () => setShowPassword((show) => !show);
    const theme = useTheme();
    const { login } = useAuth()
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const [formValues, setFormValues] = useState<loginData>({
        email: '',
        password: ''
    })
    const [error, setError] = useState('')
    const [isLoading, setIsLoading] = useState(false)

    const emailRegex = /^[\w\.-]+@[a-zA-Z\d\.-]+\.[a-zA-Z]{2,}$/;
    const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        setError('')
        setFormValues((prevValues) => ({
            ...prevValues,
            [name]: value
        }))
    }

    const isEmailCorrect = (email: string) => {
        if (emailRegex.test(email)) {
            return email
        }
        else {
            return undefined
        }
    }
    const handleFormSubmit = async () => {
        setIsLoading(true)
        const { email, password } = formValues
        if (!email || !password) {
            setError('Missing Fields')
        }
        else if (email && !isEmailCorrect(email)) {
            setError('Incorrect Credential Format')
        }
        else {
            await login(email, password)
        }
        setIsLoading(false)
    }
    return (
        <>
            {isLoading && <LoadingScreen />}
            <Box sx={{
                width: '100%', height: '100vh', display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <Grid container direction={'column'} sx={{ width: isDesktop ? '50%' : '80%', boxShadow: 3, p: '5%', borderRadius: 5, margin: 0 }}>
                    <Typography sx={{
                        fontWeight: 'bold',
                        fontSize: '1.3rem',
                        color: 'green'
                    }}>
                        Zenith Clinic
                    </Typography>
                    <Typography sx={{
                        fontSize: '0.8rem',
                        mb: 1
                    }}>
                        Doctor's Portal for consultation with scheduled patients
                    </Typography>
                    <Divider sx={{ mt: 1, mb: 5 }} />
                    <TextField
                        label='Email'
                        size="medium"
                        value={formValues.email}
                        name='email'
                        fullWidth={true}
                        sx={{
                            mb: 2,
                            ...inputStyle,
                        }}
                        onChange={handleFormChange}
                        helperText={error ? error : undefined}
                        error={error ? true : false}
                    />
                    <FormControl variant="outlined" fullWidth={true} size="medium" sx={{
                        mb: 2,
                        ...inputStyle,
                        '& .MuiInputLabel-root': {
                            color: error ? '#d32f2f' : '#3B3B3B',
                        },
                    }}>
                        <InputLabel htmlFor="outlined-adornment-password" >
                            <Typography>
                                Password
                            </Typography></InputLabel>
                        <OutlinedInput
                            name="password"
                            id="outlined-adornment-password"
                            type={showPassword ? 'text' : 'password'}
                            endAdornment={
                                <InputAdornment position="end">
                                    <IconButton
                                        sx={{
                                            mr: 1
                                        }}
                                        aria-label="toggle password visibility"
                                        onClick={handleClickShowPassword}
                                        edge="end"
                                    >
                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                    </IconButton>
                                </InputAdornment>
                            }
                            label="Password"
                            value={formValues.password}
                            onChange={handleFormChange}
                            error={error ? true : false}
                        />

                        {error && <FormHelperText sx={{ color: '#d32f2f' }}>
                            {error}
                        </FormHelperText>}
                    </FormControl>

                    <Button variant="contained" sx={{
                        backgroundColor: 'green',
                        borderRadius: '30px',
                        width: '100%',
                        height: '50px'
                    }}
                        onClick={handleFormSubmit}
                    >
                        <Typography sx={{ fontWeight: '500' }}>
                            Login
                        </Typography>
                    </Button>
                </Grid>
            </Box>
        </>
    )
}
export default Login