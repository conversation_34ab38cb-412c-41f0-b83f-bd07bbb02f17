{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "checks": "tsc -b", "build": "tsc -b && vite build", "type-check": "tsc", "precommit": "pnpm type-check && pnpm checks", "prod": "vite preview"}, "dependencies": {"@daily-co/daily-js": "^0.73.0", "@daily-co/daily-react": "^0.21.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.1.5", "@mui/material": "^6.1.3", "@mui/x-date-pickers": "^7.24.0", "@supabase/supabase-js": "^2.45.6", "@tanstack/react-location": "^3.7.4", "@types/luxon": "^3.4.2", "axios": "^1.7.7", "axios-mock-adapter": "^2.1.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "luxon": "^3.5.0", "moment": "^2.30.1", "nanoid": "^5.1.5", "moment-timezone": "^0.6.0", "notistack": "^3.0.2", "react": "^18.3.1", "react-calendar": "^5.1.0", "react-dom": "^18.3.1", "react-timezone-select": "^3.2.8", "react-to-print": "^3.1.0", "stream-chat": "^8.57.6", "stream-chat-react": "^12.13.1", "uuid": "^11.0.3", "vite-plugin-svgr": "^4.2.0"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}